{"rules": {"ignoreFiles": ["node_modules/**/*", "dist/**/*", "build/**/*", ".git/**/*", "**/*.min.js", "**/*.map", "coverage/**/*", ".next/**/*", "tmp/**/*", "*.log"], "maxFileSize": "1MB", "maxFilesToIndex": 10000, "maxLineLength": 120, "fileTypes": {"include": ["*.js", "*.jsx", "*.ts", "*.tsx", "*.py", "*.java", "*.go", "*.html", "*.css", "*.scss", "*.json", "*.md", "*.yaml", "*.yml"], "exclude": ["*.lock", "*.svg", "*.png", "*.jpg", "*.jpeg", "*.gif", "*.ico", "*.woff", "*.woff2", "*.ttf", "*.eot"]}}}