# 🚚 Kang Ider Integration - Complete Guide

## 📋 Overview

Sistem distribusi ke Kang Ider telah diintegrasikan dengan menggunakan tabel yang sudah ada:
- **SalesPlanned** - Perencanaan penjualan untuk Kang Ider
- **SalesPlannedItem** - Detail item dalam sales plan
- **kas_harian** - Ka<PERSON> harian Kang Ider
- **stock_harian** - Stock harian per produk per Kang Ider

## 🔄 Workflow Distribusi Kang Ider

### **Alur Lengkap:**
```
1. Team Kang Ider → Buat SalesPlanned (status: "draft")
2. Team Kang Ider → Review & Publish (status: "published") 
3. Orang <PERSON>ng → Lihat SalesPlanned yang "published"
4. Orang Gudang → Klik "Load Ider" (status: "load ider")
5. System → Auto create kas_harian + stock_harian
6. System → Auto reduce production_stock
7. System → Record stock_movement (outgoing)
```

### **Status Flow:**
- **draft** → Team <PERSON> Ider masih menyusun
- **published** → Siap untuk diambil Kang Ider
- **load ider** → Sudah diambil, masuk ke kas_harian

## 🗂️ File Structure

```
app/production/kangider/
├── page.tsx                    # Kang Ider distribution management
├── kas-harian/
│   └── page.tsx               # Kas harian overview
└── [id]/
    └── page.tsx               # Detail kas harian (future)

app/api/production/kangider/
└── route.ts                   # API for Kang Ider workflow

lib/directus.ts                # Enhanced with KasHarian & StockHarian
```

## 🎯 Fitur yang Diimplementasi

### **1. 📊 Kang Ider Distribution Page** (`/production/kangider`)

**Fitur:**
- ✅ **View all SalesPlanned** - Semua sales plan dengan filter
- ✅ **Filter by status** - Draft, Published, Load Ider
- ✅ **Filter by Kang Ider** - Per individual Kang Ider
- ✅ **Filter by date** - Range tanggal
- ✅ **Search functionality** - By Kang Ider name atau ID
- ✅ **Status management** - Update status dengan workflow
- ✅ **Load Ider action** - Process distribusi dengan satu klik

**Actions Available:**
- **View** - Lihat detail sales plan
- **Edit** - Edit sales plan (hanya status draft)
- **Load Ider** - Process distribusi (hanya status published)

### **2. 💰 Kas Harian Management** (`/production/kangider/kas-harian`)

**Fitur:**
- ✅ **Overview kas harian** - Semua record kas harian
- ✅ **Summary cards** - Total kas, penjualan, stock
- ✅ **Filter by Kang Ider** - Per individual Kang Ider
- ✅ **Filter by date** - Range tanggal
- ✅ **Stock variance tracking** - Perubahan stock awal vs akhir
- ✅ **Related stock harian** - Link ke detail stock per produk

**Metrics Displayed:**
- Total kas harian records
- Total penjualan amount
- Total stock awal
- Total stock akhir
- Stock variance indicators

### **3. 🔧 API Integration** (`/api/production/kangider`)

**Endpoints:**
- **GET** - Fetch sales plans dengan filter
- **POST** - Process "load ider" workflow
- **PATCH** - Update sales plan status

**Workflow Processing:**
- Update SalesPlanned status
- Create kas_harian record
- Create stock_harian records per item
- Reduce production_stock
- Record stock_movement

## 📊 Database Integration

### **Tables Used:**

#### **SalesPlanned**
```typescript
interface SalesPlanned {
  id: string
  kangider: string              // UUID ke KangIder
  date: string                  // YYYY-MM-DD
  status: "draft" | "published" | "load ider"
  total_cup: number
  items?: SalesPlannedItem[]
}
```

#### **SalesPlannedItem**
```typescript
interface SalesPlannedItem {
  id?: string
  sales_planned_id?: string
  produk: string                // UUID ke Produk
  quantity: number
}
```

#### **KasHarian**
```typescript
interface KasHarian {
  id: string
  status: string
  tanggal: string               // YYYY-MM-DD
  waktu_buka?: string           // HH:mm:ss
  waktu_tutup?: string          // HH:mm:ss
  stok_awal?: number
  stock_akhir?: number
  petty_cash?: number
  total_penjualan?: number
  stock_produk?: any            // JSON produk
  keterangan?: string
  idkangider: string            // UUID ke KangIder
  
  // Relations
  kangider?: KangIder
  stock_harian?: StockHarian[]
}
```

#### **StockHarian**
```typescript
interface StockHarian {
  id: string
  id_produk: string             // UUID ke Produk
  id_kangider: string           // UUID ke KangIder
  tanggal_stock: string         // YYYY-MM-DD
  stock_awal: number
  stock_akhir: number
  status: string
  kas_harian: string            // UUID ke KasHarian
  
  // Relations
  kangider?: KangIder
  kasHarian?: KasHarian
}
```

## 🔄 API Functions

### **Enhanced Directus Functions:**

```typescript
// Kas Harian Management
getKasHarian(filters?: any)
createKasHarian(kasHarian: Omit<KasHarian, "id" | "date_created">)
updateKasHarian(id: string, kasHarian: Partial<KasHarian>)

// Stock Harian Management  
getStockHarian(filters?: any)
createStockHarian(stockHarian: Omit<StockHarian, "id" | "date_created">)
updateStockHarian(id: string, stockHarian: Partial<StockHarian>)

// Enhanced Sales Plan Workflow
processSalesPlanLoadIder(salesPlanId: string)
updateSalesPlanWithWorkflow(id: string, data: Partial<SalesPlanned>)
```

### **Workflow Function:**
```typescript
processSalesPlanLoadIder(salesPlanId: string) {
  // 1. Get SalesPlanned with items
  // 2. Create kas_harian record
  // 3. Create stock_harian records per item
  // 4. Reduce production_stock per item
  // 5. Record stock_movement (outgoing)
  // 6. Return complete workflow result
}
```

## 🎨 UI Components

### **Status Badges:**
- **Draft** - Clock icon, outline variant
- **Published** - AlertCircle icon, secondary variant  
- **Load Ider** - CheckCircle icon, default variant

### **Action Buttons:**
- **View** - Eye icon untuk semua status
- **Edit** - Edit icon hanya untuk draft
- **Load Ider** - Truck icon hanya untuk published

### **Filter Components:**
- Search input dengan Search icon
- Status select dropdown
- Kang Ider select dropdown
- Date input
- Clear filters button

## 🔍 Testing Scenarios

### **1. Complete Workflow Test:**
```
1. Create SalesPlanned (status: "draft")
2. Update to "published" 
3. Process "load ider" dari production page
4. Verify kas_harian created
5. Verify stock_harian created per item
6. Verify production_stock reduced
7. Verify stock_movement recorded
```

### **2. Filter Testing:**
```
1. Test search by Kang Ider name
2. Test filter by status
3. Test filter by Kang Ider
4. Test filter by date
5. Test clear filters
```

### **3. Data Validation:**
```
1. Verify stock calculations
2. Verify kas_harian totals
3. Verify stock_harian per product
4. Verify production_stock reduction
```

## 📈 Business Benefits

### **For Gudang Team:**
- ✅ **Centralized view** - Semua sales plan dalam satu tempat
- ✅ **Easy processing** - One-click "Load Ider" 
- ✅ **Automatic stock management** - Auto reduce production stock
- ✅ **Audit trail** - Complete tracking via stock_movement

### **For Kang Ider Team:**
- ✅ **Clear workflow** - Draft → Published → Load Ider
- ✅ **Visibility** - Status tracking yang jelas
- ✅ **Integration** - Seamless dengan kas_harian system

### **For Management:**
- ✅ **Real-time tracking** - Live status updates
- ✅ **Performance metrics** - Kas harian analytics
- ✅ **Stock control** - Automatic stock management
- ✅ **Data integrity** - Consistent data flow

## 🚀 Next Steps

### **Phase 1 - Current Implementation:**
- ✅ Basic workflow (Draft → Published → Load Ider)
- ✅ Kas harian creation
- ✅ Stock harian creation
- ✅ Production stock reduction

### **Phase 2 - Enhancements:**
- 📋 **Kas harian detail page** - Individual kas management
- 📋 **Stock adjustment** - Manual stock corrections
- 📋 **Return processing** - Handle returns from Kang Ider
- 📋 **Analytics dashboard** - Performance metrics

### **Phase 3 - Advanced Features:**
- 📋 **Mobile app integration** - Field updates
- 📋 **Real-time notifications** - Status change alerts
- 📋 **Automated reporting** - Daily/weekly reports
- 📋 **Forecasting** - Demand prediction

## 🔧 Configuration

### **Environment Variables:**
```env
DIRECTUS_URL=your_directus_url
DIRECTUS_TOKEN=your_directus_token
```

### **Required Permissions:**
- Read/Write access to: sales_planned, kas_harian, stock_harian
- Read access to: kangider, produk, production_stock
- Write access to: stock_movement

Sistem Kang Ider integration sekarang **fully functional** dengan workflow yang complete dan terintegrasi dengan production management! 🎉
