'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import axios from 'axios';

export default function AccurateIntegrationClient() {
  const searchParams = useSearchParams();
  const [accessToken, setAccessToken] = useState('');
  const [refreshToken, setRefreshToken] = useState('');
  const [sessionId, setSessionId] = useState('');
  const [dbAlias, setDbAlias] = useState('');
  const [result, setResult] = useState<any>(null);
  const [excelFile, setExcelFile] = useState<File | null>(null);
  const [uploadResponse, setUploadResponse] = useState<any>(null);
  const [syncResponse, setSyncResponse] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [apiToken, setApiToken] = useState('');
  const [apiHost, setApiHost] = useState('');
  const [apiTokenResponse, setApiTokenResponse] = useState<any>(null);

  // Get client ID from environment variables
  const clientId = process.env.NEXT_PUBLIC_ACCURATE_CLIENT_ID ?? '';
  const redirectUri = `${typeof window !== 'undefined' ? window.location.origin : ''}/api/oauth/callback`;

  // Check for tokens in URL if coming back from Accurate OAuth
  useEffect(() => {
    const token = searchParams?.get('access_token');
    const refresh = searchParams?.get('refresh_token');
    
    if (token) {
      setAccessToken(token);
    }
    
    if (refresh) {
      setRefreshToken(refresh);
    }
  }, [searchParams]);

  const handleLogin = () => {
    const authUrl = `https://account.accurate.id/oauth/authorize?client_id=${clientId}&response_type=code&redirect_uri=${redirectUri}&scope=accounting`;
    window.location.href = authUrl;
  };

  const fetchDbAlias = async () => {
    try {
      setLoading(true);
      const res = await axios.post('/api/accurate/open-db', { access_token: accessToken }, {
        headers: {
          Authorization: `Bearer ${process.env.NEXT_PUBLIC_DIRECTUS_TOKEN}`, // Sesuaikan .env
        },
      });
      if (res.data.data && res.data.data.length > 0) {
        const alias = res.data.data[0].dbAlias;
        setDbAlias(alias);
      }
    } catch (error) {
      console.error('Error fetching DB alias:', error);
      setResult({ error: 'Failed to fetch database alias' });
    } finally {
      setLoading(false);
    }
  };

  const openSession = async () => {
    try {
      setLoading(true);
      const res = await axios.post('/api/accurate/open-session', {
        access_token: accessToken,
        dbAlias,
      });
      setSessionId(res.data.sessionId);
    } catch (error) {
      console.error('Error opening session:', error);
      setResult({ error: 'Failed to open session' });
    } finally {
      setLoading(false);
    }
  };

  const submitJobOrder = async () => {
    try {
      setLoading(true);
      const jobOrderData = {
        transDate: new Date().toISOString().split('T')[0],
        description: 'Job Order dari Next.js',
        detailItem: [
          {
            itemNo: 'ITEM001',
            quantity: 10,
            itemUnitName: 'pcs',
          },
        ],
      };

      const res = await axios.post('/api/accurate/job-order', {
        access_token: accessToken,
        sessionId,
        jobOrderData,
        host: apiHost || undefined,
      });
      
      setResult(res.data);
    } catch (error) {
      console.error('Error submitting job order:', error);
      setResult({ error: 'Failed to submit job order' });
    } finally {
      setLoading(false);
    }
  };

  const handleExcelChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setExcelFile(e.target.files[0]);
    }
  };

  const handleExcelUpload = async () => {
    if (!excelFile) return;
    
    try {
      setLoading(true);
      const formData = new FormData();
      formData.append('file', excelFile);

      const res = await axios.post('/api/directus/import-produk-gudang', formData);
      setUploadResponse(res.data);
    } catch (error) {
      console.error('Error uploading Excel:', error);
      setUploadResponse({ error: 'Failed to upload Excel file' });
    } finally {
      setLoading(false);
    }
  };

  const syncIncomingToAccurate = async () => {
    try {
      setLoading(true);
      const res = await axios.post('/api/accurate/sync-incoming-to-purchase', {
        access_token: accessToken,
        sessionId,
        host: apiHost || undefined,
      });
      setSyncResponse(res.data);
    } catch (error) {
      console.error('Error syncing to Accurate:', error);
      setSyncResponse({ error: 'Failed to sync data to Accurate' });
    } finally {
      setLoading(false);
    }
  };

  const testApiToken = async () => {
    if (!apiToken) {
      alert('Masukkan API Token terlebih dahulu');
      return;
    }
    try {
      const response = await axios.post('/api/accurate/api-token', {apiToken}, {
        headers: {
          Authorization: `Bearer ${process.env.NEXT_PUBLIC_DIRECTUS_TOKEN}`, // Sesuaikan .env
        },
        
      });

      console.log('✅ Success:', response.data);
    } catch (error: any) {
      console.error('❌ Error:', error.response?.data || error.message);
    }


    // try {
    //   setLoading(true);
  
  
    //   // Panggil ke API backend Next.js (bukan langsung ke Accurate!)
    //   const res = await axios.post('/api/accurate/api-token', {
    //     headers: {
    //       Authorization: `Bearer ${process.env.NEXT_PUBLIC_DIRECTUS_TOKEN}`, // Sesuaikan .env
    //     },
    //     apiToken,
    //   });
  
    //   // Berhasil
    //   console.log('✅ API Token valid:', res.data);
    //   setApiHost(res.data.host);
    //   setApiTokenResponse(res.data);
    // } catch (error: any) {
    //   console.error('❌ Error checking API token:', error);
    //   if (error.response) {
    //     setApiTokenResponse({
    //       error: error.response.data?.message || 'Gagal verifikasi token',
    //       debug: error.response.data?.debug || null,
    //       status: error.response.status,
    //     });
    //   } else {
    //     setApiTokenResponse({ error: 'Request gagal', debug: null });
    //   }
    // } finally {
    //   setLoading(false);
    // }
  };
  

  return (
    <div className="p-8 space-y-6">
      <h1 className="text-2xl font-bold">Integrasi Accurate API</h1>

      <div className="border rounded-lg p-4 bg-blue-50 shadow-sm">
        <h2 className="text-lg font-semibold mb-3">🔑 Cek API Token Accurate</h2>
        <input
          type="text"
          placeholder="Masukkan API Token"
          value={apiToken}
          onChange={(e) => setApiToken(e.target.value)}
          className="border p-2 w-full rounded"
        />
        <button 
          onClick={testApiToken} 
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 mt-2 rounded"
          disabled={!apiToken || loading}
        >
          {loading ? 'Checking...' : 'Cek Host dari API Token'}
        </button>
        {apiHost && <p className="mt-2">✅ Host: <strong>{apiHost}</strong></p>}
        {apiTokenResponse && (
          <div className="bg-gray-50 p-3 mt-2 rounded text-sm overflow-auto max-h-40">
            <pre>{JSON.stringify(apiTokenResponse, null, 2)}</pre>
          </div>
        )}
      </div>

      <div className="border rounded-lg p-4 bg-white shadow-sm">
        <h2 className="text-lg font-semibold mb-3">Autentikasi</h2>
        
        <div className="space-y-3">
          <button 
            onClick={handleLogin} 
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
            disabled={loading}
          >
            Login ke Accurate
          </button>

          {accessToken && (
            <div className="bg-green-50 p-2 rounded text-sm">
              <p>✅ Berhasil mendapatkan token</p>
            </div>
          )}
        </div>
      </div>

      <div className="border rounded-lg p-4 bg-white shadow-sm">
        <h2 className="text-lg font-semibold mb-3">Koneksi Database</h2>
        
        <div className="space-y-3">
          <input
            type="text"
            placeholder="Access Token"
            value={accessToken}
            onChange={(e) => setAccessToken(e.target.value)}
            className="border p-2 w-full rounded"
          />
          
          <div className="flex gap-2 items-center">
            <button 
              onClick={fetchDbAlias} 
              className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded"
              disabled={!accessToken || loading}
            >
              {loading ? 'Loading...' : 'Get DB Alias'}
            </button>
            
            {dbAlias && (
              <span className="text-sm bg-gray-100 p-1 rounded">
                DB Alias: <strong>{dbAlias}</strong>
              </span>
            )}
          </div>
        </div>
      </div>

      <div className="border rounded-lg p-4 bg-white shadow-sm">
        <h2 className="text-lg font-semibold mb-3">Buka Sesi Accurate</h2>
        
        <div className="space-y-3">
          <button 
            onClick={openSession} 
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded"
            disabled={!accessToken || !dbAlias || loading}
          >
            {loading ? 'Loading...' : 'Buka Sesi'}
          </button>
          
          {sessionId && (
            <div className="bg-green-50 p-2 rounded text-sm">
              <p>Session ID: <strong>{sessionId}</strong></p>
            </div>
          )}
        </div>
      </div>

      <div className="border rounded-lg p-4 bg-white shadow-sm">
        <h2 className="text-lg font-semibold mb-3">Job Order</h2>
        
        <button 
          onClick={submitJobOrder} 
          className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded"
          disabled={!accessToken || !sessionId || loading}
        >
          {loading ? 'Loading...' : 'Submit Job Order'}
        </button>
      </div>

      <div className="border rounded-lg p-4 bg-white shadow-sm">
        <h2 className="text-lg font-semibold mb-3">Import Excel ke Directus</h2>
        
        <div className="space-y-3">
          <input 
            type="file" 
            accept=".xlsx,.xls" 
            onChange={handleExcelChange} 
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
          
          <button
            onClick={handleExcelUpload}
            className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded"
            disabled={!excelFile || loading}
          >
            {loading ? 'Loading...' : 'Upload & Import Excel'}
          </button>
          
          {uploadResponse && (
            <div className="mt-4 bg-gray-50 p-3 rounded text-sm overflow-auto max-h-40">
              <pre>{JSON.stringify(uploadResponse, null, 2)}</pre>
            </div>
          )}
        </div>
      </div>

      <div className="border rounded-lg p-4 bg-white shadow-sm">
        <h2 className="text-lg font-semibold mb-3">Sinkronkan Stok Masuk ke Accurate</h2>
        
        <div className="space-y-3">
          <button
            onClick={syncIncomingToAccurate}
            className="bg-teal-500 hover:bg-teal-600 text-white px-4 py-2 rounded"
            disabled={!accessToken || !sessionId || loading}
          >
            {loading ? 'Syncing...' : 'Sinkronkan Stok Masuk ke Purchase Invoice'}
          </button>
          
          {syncResponse && (
            <div className="mt-4 bg-gray-50 p-3 rounded text-sm overflow-auto max-h-40">
              <pre>{JSON.stringify(syncResponse, null, 2)}</pre>
            </div>
          )}
        </div>
      </div>

      {result && (
        <div className="border rounded-lg p-4 bg-white shadow-sm">
          <h2 className="text-lg font-semibold mb-3">Hasil</h2>
          <div className="bg-gray-50 p-3 rounded text-sm overflow-auto max-h-60">
            <pre>{JSON.stringify(result, null, 2)}</pre>
          </div>
        </div>
      )}
    </div>
  );
} 