'use client';

import { useState, useEffect } from 'react';
import axios from 'axios';
import { format } from 'date-fns-tz';

export default function TestApiToken() {
  const [apiToken, setApiToken] = useState('');
  const [response, setResponse] = useState<any>(null);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [endpointType, setEndpointType] = useState('default');
  const [signatureSecret, setSignatureSecret] = useState('');
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Get environment variables on load
  useEffect(() => {
    const checkEnv = async () => {
      try {
        const res = await axios.get('/api/accurate/debug-env');
        setDebugInfo(res.data);
      } catch (err) {
        console.error('Error checking environment:', err);
      }
    };
    
    checkEnv();
  }, []);

  const generateTestTimestamp = () => {
    return format(new Date(), 'dd/MM/yyyy HH:mm:ss', { timeZone: 'UTC' });
  };

  const testToken = async () => {
    setLoading(true);
    setResponse(null);
    setError(null);

    try {
      const res = await axios.post('/api/accurate/api-token', { 
        apiToken,
        endpointType,
        ...(signatureSecret ? { signatureSecret } : {})
      });
      setResponse(res.data);
    } catch (err: any) {
      console.error('Error testing API token:', err);
      setError({
        message: err.message,
        status: err.response?.status,
        statusText: err.response?.statusText,
        details: err.response?.data
      });
    } finally {
      setLoading(false);
    }
  };

  const testDirectRequest = async () => {
    setLoading(true);
    setResponse(null);
    setError(null);

    try {
      const timestamp = generateTestTimestamp();
      
      // Only proceed if we have the necessary data
      if (!apiToken) {
        throw new Error('API Token is required');
      }
      
      const headers: any = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Api-Timestamp': timestamp
      };
      
      if (apiToken.startsWith('aat.')) {
        headers['Authorization'] = `Bearer ${apiToken}`;
      }
      
      let endpoint = '';
      let method = 'POST';
      let params = {};
      
      // Select endpoint based on type
      switch (endpointType) {
        case 'info':
          endpoint = 'https://account.accurate.id/api/info.do';
          method = 'GET';
          if (!apiToken.startsWith('aat.')) {
            params = { sp: apiToken };
          }
          break;
        case 'token-info':
          endpoint = 'https://account.accurate.id/api/api-token-info.do';
          break;
        default:
          endpoint = 'https://account.accurate.id/api/api-token.do';
      }
      
      const result = method === 'GET' 
        ? await axios.get(endpoint, { headers, params })
        : await axios.post(endpoint, null, { headers });
      
      setResponse({
        data: result.data,
        timestamp,
        headers: headers,
        endpoint,
        method
      });
    } catch (err: any) {
      console.error('Error in direct request:', err);
      setError({
        message: err.message,
        status: err.response?.status,
        statusText: err.response?.statusText,
        details: err.response?.data,
        headers: err.config?.headers,
        url: err.config?.url
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Test Accurate API Token</h1>
      
      <div className="bg-white p-6 rounded-lg shadow-sm border mb-6">
        <h2 className="text-lg font-semibold mb-3">Input API Token</h2>
        
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            API Token {endpointType !== 'info' && '(starts with aat.)'}
          </label>
          <input
            type="text"
            value={apiToken}
            onChange={(e) => setApiToken(e.target.value)}
            placeholder={endpointType !== 'info' ? "aat.your-api-token-here" : "your-api-token-here"}
            className="w-full p-2 border rounded"
          />
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Endpoint Type
          </label>
          <select 
            value={endpointType}
            onChange={(e) => setEndpointType(e.target.value)}
            className="w-full p-2 border rounded"
          >
            <option value="default">Default (api-token.do)</option>
            <option value="token-info">Token Info (api-token-info.do)</option>
            <option value="info">Info (info.do)</option>
          </select>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-2 mb-4">
          <button
            onClick={testToken}
            disabled={loading || !apiToken}
            className={`px-4 py-2 rounded text-white ${
              loading || !apiToken ? 'bg-gray-400' : 'bg-blue-600 hover:bg-blue-700'
            }`}
          >
            {loading ? 'Testing...' : 'Test API Token'}
          </button>
          
          <button
            onClick={testDirectRequest}
            disabled={loading || !apiToken}
            className={`px-4 py-2 rounded text-white ${
              loading || !apiToken ? 'bg-gray-400' : 'bg-green-600 hover:bg-green-700'
            }`}
          >
            Test Direct Request
          </button>
          
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="px-4 py-2 rounded text-white bg-purple-600 hover:bg-purple-700"
          >
            {showAdvanced ? 'Hide Advanced' : 'Show Advanced'}
          </button>
        </div>

        {showAdvanced && (
          <div className="mt-4 bg-gray-50 p-4 rounded border">
            <h3 className="font-medium mb-2">Advanced Options</h3>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Custom Signature Secret (overrides env)
              </label>
              <input
                type="text"
                value={signatureSecret}
                onChange={(e) => setSignatureSecret(e.target.value)}
                placeholder="Your signature secret"
                className="w-full p-2 border rounded"
              />
            </div>

            <div className="text-sm text-gray-600">
              <p className="mb-1">Token format:</p>
              <ul className="list-disc pl-5">
                <li>For Authorization header: <code className="bg-gray-200 px-1">aat.{'{'}yourtoken{'}'}</code></li>
                <li>For "sp" parameter: <code className="bg-gray-200 px-1">yourtoken</code> (without "aat." prefix)</li>
                <li>For host extraction: <code className="bg-gray-200 px-1">aat.{'{'}host{'}'}-....</code></li>
              </ul>
            </div>
          </div>
        )}
      </div>

      {response && (
        <div className="bg-green-50 p-4 rounded-lg border border-green-200 mb-6">
          <h2 className="text-lg font-semibold text-green-800 mb-2">Success</h2>
          <div className="bg-white p-3 rounded overflow-auto max-h-96">
            <pre className="text-sm">{JSON.stringify(response, null, 2)}</pre>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 p-4 rounded-lg border border-red-200 mb-6">
          <h2 className="text-lg font-semibold text-red-800 mb-2">Error</h2>
          <div className="mb-2">
            <span className="font-semibold">Status:</span> {error.status} {error.statusText}
          </div>
          <div className="mb-2">
            <span className="font-semibold">Message:</span> {error.message}
          </div>
          <div className="bg-white p-3 rounded overflow-auto max-h-96">
            <pre className="text-sm">{JSON.stringify(error.details, null, 2)}</pre>
          </div>
        </div>
      )}

      {debugInfo && (
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-6">
          <h2 className="text-lg font-semibold text-blue-800 mb-2">Environment Status</h2>
          <div className="bg-white p-3 rounded overflow-auto max-h-40">
            <pre className="text-sm">{JSON.stringify(debugInfo, null, 2)}</pre>
          </div>
        </div>
      )}

      <div className="mt-8 bg-gray-50 p-4 rounded-lg border">
        <h2 className="text-lg font-semibold mb-2">Troubleshooting Guide</h2>
        <ul className="list-disc pl-5 space-y-2 text-sm">
          <li>Ensure your API token starts with <code className="bg-gray-100 px-1 rounded">aat.</code> (except for info endpoint with sp parameter)</li>
          <li>Verify that <code className="bg-gray-100 px-1 rounded">ACCURATE_SIGNATURE_SECRET</code> is set in your environment</li>
          <li>Check that your API token is active in the Accurate Developer Console</li>
          <li>Make sure the API token has not expired</li>
          <li>Verify that your server has the correct time set (for timestamp validation)</li>
          <li>Try the different endpoint types to see which one works with your token</li>
          <li>If using a custom domain, check if you need to update the base URL</li>
        </ul>
        
        <div className="mt-4 text-sm text-gray-600">
          <p><strong>Common 401 Unauthorized causes:</strong></p>
          <ol className="list-decimal pl-5 space-y-1 mt-1">
            <li>Token format incorrect (missing "aat." prefix)</li>
            <li>Signature generation failing (incorrect secret or implementation)</li>
            <li>Token expired or not activated in Developer Console</li>
            <li>Clock synchronization issues (timestamp validation fails)</li>
            <li>API endpoint changes (try different endpoint types)</li>
          </ol>
        </div>
      </div>
    </div>
  );
} 