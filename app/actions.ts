"use server"

import {
  type <PERSON>duk<PERSON>udang,
  type StockMovement,
  type DailyProduction,
  type Vendor,
  type ProdukGudangConversion,
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  getStockMovements,
  createStockMovement,
  getCurrentStock,
  getCoffeeProducts,
  getDailyProduction,
  createDailyProduction,
  updateDailyProduction,
  deleteDailyProduction,
  getProductionWithDetails,
  getProductionSummary,
  handleNewStockMovement,
  // Vendor functions
  getVendors,
  getVendor,
  createVendor,
  updateVendor,
  deleteVendor,
  // Conversion functions
  getConversionUnits,
  getAllConversionUnits,
  createConversionUnit,
  updateConversionUnit,
  deleteConversionUnit,
  getAvailableUnitsForProduct,
  createStockMovementWithConversion,
} from "@/lib/directus"
import { revalidatePath } from "next/cache"

// Product actions
export async function fetchProducts() {
  try {
    const products = await getProducts()
    return { success: true, data: products }
  } catch (error) {
    console.error("Error fetching products:", error)
    return { success: false, error: "Failed to fetch products" }
  }
}

export async function fetchProduct(id: string) {
  try {
    const products = await getProduct(id)
    if (products && products.length > 0) {
      return { success: true, data: products[0] }
    }
    return { success: false, error: "Product not found" }
  } catch (error) {
    console.error("Error fetching product:", error)
    return { success: false, error: "Failed to fetch product" }
  }
}

export async function addProduct(product: ProdukGudang) {
  try {
    const newProduct = await createProduct(product)
    revalidatePath("/products")
    return { success: true, data: newProduct }
  } catch (error) {
    console.error("Error creating product:", error)
    return { success: false, error: "Failed to create product" }
  }
}

export async function editProduct(id: string, product: Partial<ProdukGudang>) {
  try {
    const updatedProduct = await updateProduct(id, product)
    revalidatePath(`/products/${id}`)
    revalidatePath("/products")
    return { success: true, data: updatedProduct }
  } catch (error) {
    console.error("Error updating product:", error)
    return { success: false, error: "Failed to update product" }
  }
}

export async function removeProduct(id: string) {
  try {
    await deleteProduct(id)
    revalidatePath("/products")
    return { success: true }
  } catch (error) {
    console.error("Error deleting product:", error)
    return { success: false, error: "Failed to delete product" }
  }
}

// Stock movement actions
export async function fetchStockMovements(filters?: any) {
  try {
    const movements = await getStockMovements(filters)
    return { success: true, data: movements }
  } catch (error) {
    console.error("Error fetching stock movements:", error)
    return { success: false, error: "Failed to fetch stock movements" }
  }
}

export async function addStockMovement(movement: StockMovement) {
  try {
    // Try to create the stock movement
    await handleNewStockMovement(movement)

    // Revalidate paths
    revalidatePath("/stock")
    if (movement.produk_gudang_id) {
      revalidatePath(`/products/${movement.produk_gudang_id}`)
    }

    return { success: true }
  } catch (error) {
    console.error("Error creating stock movement:", error)

    // Check if it's an authentication error
    const errorMessage = error instanceof Error ? error.message : "Unknown error"
    if (errorMessage.includes("Authentication") || errorMessage.includes("token") || errorMessage.includes("permission")) {
      return { success: false, error: "Authentication failed. Please log in again.", authError: true }
    }

    return { success: false, error: "Failed to create stock movement" }
  }
}

// Enhanced stock movement with conversion
export async function addStockMovementWithConversion(movement: {
  produk_gudang_id: string
  date: string
  type: "incoming" | "outgoing"
  quantity: number
  unit_used?: string
  vendor_id?: string
  purchase_price?: number
  reference_number?: string
  notes?: string
  created_by?: string
}) {
  try {
    // Try to create the stock movement with conversion
    await createStockMovementWithConversion(movement)

    // Revalidate paths
    revalidatePath("/stock")
    if (movement.produk_gudang_id) {
      revalidatePath(`/products/${movement.produk_gudang_id}`)
    }

    return { success: true }
  } catch (error) {
    console.error("Error creating stock movement with conversion:", error)

    // Check if it's an authentication error
    const errorMessage = error instanceof Error ? error.message : "Unknown error"
    if (errorMessage.includes("Authentication") || errorMessage.includes("token") || errorMessage.includes("permission")) {
      return { success: false, error: "Authentication failed. Please log in again.", authError: true }
    }

    return { success: false, error: "Failed to create stock movement" }
  }
}

// Current stock actions
export async function fetchCurrentStock() {
  try {
    const stock = await getCurrentStock()
    return { success: true, data: stock }
  } catch (error) {
    console.error("Error fetching current stock:", error)
    return { success: false, error: "Failed to fetch current stock" }
  }
}

// Dashboard data
export async function fetchDashboardData() {
  try {
    const [products, currentStock, recentMovements] = await Promise.all([
      getProducts(),
      getCurrentStock(),
      getStockMovements({ limit: 5, sort: ["-date"] }),
    ])

    // Calculate low stock products
    const lowStockProducts = products.filter((product) => {
      const stockItem = currentStock.find((item) => item.produk_gudang_id === product.id)
      return stockItem && stockItem.current_stock < product.min_stock
    })

    // Calculate total incoming/outgoing for last 30 days
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const recentMovementsFilter = {
      date: {
        _gte: thirtyDaysAgo.toISOString().split("T")[0],
      },
    }

    const recentAllMovements = await getStockMovements(recentMovementsFilter)

    const totalIncoming = recentAllMovements
      .filter((m) => m.type === "incoming")
      .reduce((sum, m) => sum + m.quantity, 0)

    const totalOutgoing = recentAllMovements
      .filter((m) => m.type === "outgoing")
      .reduce((sum, m) => sum + m.quantity, 0)

    return {
      success: true,
      data: {
        totalProducts: products.length,
        lowStockProducts,
        recentMovements,
        totalIncoming,
        totalOutgoing,
      },
    }
  } catch (error) {
    console.error("Error fetching dashboard data:", error)
    return { success: false, error: "Failed to fetch dashboard data" }
  }
}

// New actions for coffee products
export async function fetchCoffeeProducts() {
  try {
    // Add authentication token explicitly
    const products = await getCoffeeProducts()

    // Ensure we're returning an array even if the response is unexpected
    if (!Array.isArray(products)) {
      console.error("Unexpected response format from Directus:", products)
      return { success: true, data: [] }
    }

    return { success: true, data: products }
  } catch (error) {
    console.error("Error fetching coffee products:", error)
    return { success: false, error: "Failed to fetch coffee products", data: [] }
  }
}

// New actions for daily production
export async function fetchDailyProduction(filters?: any) {
  try {
    const productions = await getDailyProduction(filters)
    return { success: true, data: productions }
  } catch (error) {
    console.error("Error fetching daily production:", error)
    return { success: false, error: "Failed to fetch daily production" }
  }
}

// Update the fetchProductionWithDetails function to handle potential errors better

export async function fetchProductionWithDetails(filters?: any) {
  try {
    const productions = await getProductionWithDetails(filters)

    // Ensure we're returning an array even if the response is unexpected
    if (!Array.isArray(productions)) {
      console.error("Unexpected response format from Directus:", productions)
      return { success: true, data: [] }
    }

    return { success: true, data: productions }
  } catch (error) {
    console.error("Error fetching production with details:", error)
    return { success: false, error: "Failed to fetch production details", data: [] }
  }
}

export async function addDailyProduction(production: DailyProduction) {
  try {
    const newProduction = await createDailyProduction(production)
    revalidatePath("/production")
    revalidatePath("/production/dashboard")
    return { success: true, data: newProduction }
  } catch (error) {
    console.error("Error creating daily production:", error)
    return { success: false, error: "Failed to create daily production" }
  }
}

export async function editDailyProduction(id: string, production: Partial<DailyProduction>) {
  try {
    const updatedProduction = await updateDailyProduction(id, production)
    revalidatePath("/production")
    revalidatePath("/production/dashboard")
    return { success: true, data: updatedProduction }
  } catch (error) {
    console.error("Error updating daily production:", error)
    return { success: false, error: "Failed to update daily production" }
  }
}

export async function removeDailyProduction(id: string) {
  try {
    await deleteDailyProduction(id)
    revalidatePath("/production")
    revalidatePath("/production/dashboard")
    return { success: true }
  } catch (error) {
    console.error("Error deleting daily production:", error)
    return { success: false, error: "Failed to delete daily production" }
  }
}

// Production dashboard data
export async function fetchProductionDashboardData() {
  try {
    // Get date range for last 7 days
    const today = new Date()
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(today.getDate() - 7)

    const startDate = sevenDaysAgo.toISOString().split("T")[0]
    const endDate = today.toISOString().split("T")[0]

    // Get production data for last 7 days
    const productionData = await getProductionSummary(startDate, endDate)

    // Get all coffee products
    const coffeeProducts = await getCoffeeProducts()

    // Calculate total production by product
    const productionByProduct = coffeeProducts.map((product) => {
      const productProductions = productionData.filter((p) => p.produk_id === product.id)
      const totalProduced = productProductions.reduce((sum, p) => sum + p.quantity_produced, 0)
      return {
        product,
        totalProduced,
        productions: productProductions,
      }
    })

    // Calculate daily totals
    const dailyTotals = []
    for (let i = 0; i < 7; i++) {
      const date = new Date(sevenDaysAgo)
      date.setDate(sevenDaysAgo.getDate() + i)
      const dateString = date.toISOString().split("T")[0]

      const dayProductions = productionData.filter((p) => p.date === dateString)
      const totalForDay = dayProductions.reduce((sum, p) => sum + p.quantity_produced, 0)

      dailyTotals.push({
        date: dateString,
        total: totalForDay,
        productions: dayProductions,
      })
    }

    return {
      success: true,
      data: {
        productionByProduct,
        dailyTotals,
        totalProduction: productionData.reduce((sum, p) => sum + p.quantity_produced, 0),
        coffeeProducts,
      },
    }
  } catch (error) {
    console.error("Error fetching production dashboard data:", error)
    return { success: false, error: "Failed to fetch production dashboard data" }
  }
}

// ===== VENDOR ACTIONS =====
export async function fetchVendors() {
  try {
    const vendors = await getVendors()
    return { success: true, data: vendors }
  } catch (error) {
    console.error("Error fetching vendors:", error)
    return { success: false, error: "Failed to fetch vendors" }
  }
}

export async function fetchVendor(id: string) {
  try {
    const vendors = await getVendor(id)
    if (vendors && vendors.length > 0) {
      return { success: true, data: vendors[0] }
    }
    return { success: false, error: "Vendor not found" }
  } catch (error) {
    console.error("Error fetching vendor:", error)
    return { success: false, error: "Failed to fetch vendor" }
  }
}

export async function addVendor(vendor: Omit<Vendor, "id" | "date_created" | "date_updated">) {
  try {
    const newVendor = await createVendor(vendor)
    revalidatePath("/vendors")
    return { success: true, data: newVendor }
  } catch (error) {
    console.error("Error creating vendor:", error)
    return { success: false, error: "Failed to create vendor" }
  }
}

export async function editVendor(id: string, vendor: Partial<Vendor>) {
  try {
    const updatedVendor = await updateVendor(id, vendor)
    revalidatePath(`/vendors/${id}`)
    revalidatePath("/vendors")
    return { success: true, data: updatedVendor }
  } catch (error) {
    console.error("Error updating vendor:", error)
    return { success: false, error: "Failed to update vendor" }
  }
}

export async function removeVendor(id: string) {
  try {
    await deleteVendor(id)
    revalidatePath("/vendors")
    return { success: true }
  } catch (error) {
    console.error("Error deleting vendor:", error)
    return { success: false, error: "Failed to delete vendor" }
  }
}

// ===== CONVERSION UNIT ACTIONS =====
export async function fetchConversionUnits(produkGudangId: string) {
  try {
    const conversions = await getConversionUnits(produkGudangId)
    return { success: true, data: conversions }
  } catch (error) {
    console.error("Error fetching conversion units:", error)
    return { success: false, error: "Failed to fetch conversion units" }
  }
}

export async function fetchAllConversionUnits() {
  try {
    const conversions = await getAllConversionUnits()
    return { success: true, data: conversions }
  } catch (error) {
    console.error("Error fetching all conversion units:", error)
    return { success: false, error: "Failed to fetch conversion units" }
  }
}

export async function addConversionUnit(conversion: Omit<ProdukGudangConversion, "id">) {
  try {
    const newConversion = await createConversionUnit(conversion)
    revalidatePath(`/products/${conversion.id_produk_gudang}`)
    revalidatePath("/products")
    return { success: true, data: newConversion }
  } catch (error) {
    console.error("Error creating conversion unit:", error)
    return { success: false, error: "Failed to create conversion unit" }
  }
}

export async function editConversionUnit(id: string, conversion: Partial<ProdukGudangConversion>) {
  try {
    const updatedConversion = await updateConversionUnit(id, conversion)
    if (conversion.id_produk_gudang) {
      revalidatePath(`/products/${conversion.id_produk_gudang}`)
    }
    revalidatePath("/products")
    return { success: true, data: updatedConversion }
  } catch (error) {
    console.error("Error updating conversion unit:", error)
    return { success: false, error: "Failed to update conversion unit" }
  }
}

export async function removeConversionUnit(id: string, produkGudangId?: string) {
  try {
    await deleteConversionUnit(id)
    if (produkGudangId) {
      revalidatePath(`/products/${produkGudangId}`)
    }
    revalidatePath("/products")
    return { success: true }
  } catch (error) {
    console.error("Error deleting conversion unit:", error)
    return { success: false, error: "Failed to delete conversion unit" }
  }
}

export async function fetchAvailableUnitsForProduct(produkGudangId: string) {
  try {
    const units = await getAvailableUnitsForProduct(produkGudangId)
    return { success: true, data: units }
  } catch (error) {
    console.error("Error fetching available units:", error)
    return { success: false, error: "Failed to fetch available units" }
  }
}

// ===== BULK MOVEMENT HISTORY ACTIONS =====
export async function fetchBulkMovementHistory(params: {
  page?: number
  limit?: number
  search?: string
  type?: string
  vendor?: string
  dateFrom?: string
  dateTo?: string
}) {
  try {
    const searchParams = new URLSearchParams()

    if (params.page) searchParams.set('page', params.page.toString())
    if (params.limit) searchParams.set('limit', params.limit.toString())
    if (params.search) searchParams.set('search', params.search)
    if (params.type && params.type !== 'all') searchParams.set('type', params.type)
    if (params.vendor && params.vendor !== 'all') searchParams.set('vendor', params.vendor)
    if (params.dateFrom) searchParams.set('dateFrom', params.dateFrom)
    if (params.dateTo) searchParams.set('dateTo', params.dateTo)

    const response = await fetch(`/api/stock/bulk-movement/history?${searchParams}`)

    if (!response.ok) {
      throw new Error('Failed to fetch bulk movement history')
    }

    const result = await response.json()
    return result
  } catch (error) {
    console.error("Error fetching bulk movement history:", error)
    return {
      success: false,
      error: "Failed to fetch bulk movement history",
      data: [],
      pagination: {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      }
    }
  }
}

