import axios from 'axios';

export async function callAccurateApi({
  endpoint,
  method = 'POST',
  token,
  signature,
  timestamp,
  data = null,
  params = null,
  extraHeaders = {},
}: {
  endpoint: string;
  method?: 'POST' | 'GET';
  token: string;
  signature: string;
  timestamp: string;
  data?: any;
  params?: any;
  extraHeaders?: Record<string, string>;
}) {
  const url = `https://zeus.accurate.id/api/${endpoint}`;
  const headers = {
    Accept: '*/*',
    Authorization: `Bearer ${token}`,
    'X-Api-Timestamp': timestamp,
    'X-Api-Signature': signature,
    ...extraHeaders,
  };

  if (method === 'POST') {
    return axios.post(url, data, { headers, params });
  } else {
    return axios.get(url, { headers, params });
  }
} 