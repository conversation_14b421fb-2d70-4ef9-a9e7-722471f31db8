// pages/api/accurate/api-token.ts
import { NextResponse } from 'next/server';
import { format } from 'date-fns-tz';
import crypto from 'crypto';
import { callAccurateApi } from '../accurateApi';

/**
 * Accurate API token verifier – works with Postman‑proven format
 * Headers that succeed in Postman:
 *  X-Api-Timestamp: 2025-05-14T00:37:16.035+07:00  (ISO 8601 with offset)
 *  X-Api-Signature: hex digest of HMAC‑SHA256(timestamp, signatureSecret)
 */
export async function POST(req: Request) {
  // Use machine local offset (Asia/Jakarta) in ISO‑8601 +07:00 format
  console.log('🔑 API Token Verifier');
  const timestamp = format(new Date(), "yyyy-MM-dd'T'HH:mm:ss.SSSXXX", {
    timeZone: 'Asia/Jakarta',
  });
  let signature: string | undefined = undefined;

  try {
    const { apiToken } = await req.json();

    if (!apiToken) {
      return NextResponse.json({ error: 'API token is required' }, { status: 400 });
    }
    if (!apiToken.startsWith('aat.')) {
      return NextResponse.json({ error: 'Token must start with "aat."' }, { status: 400 });
    }

    const secret = process.env.ACCURATE_SIGNATURE_SECRET;
    if (!secret) {
      return NextResponse.json({ error: 'Missing ACCURATE_SIGNATURE_SECRET' }, { status: 500 });
    }

    // Accurate dev docs example shows hex signature; reproduce that
    signature = crypto.createHmac('sha256', secret).update(timestamp).digest('hex');

    const response = await callAccurateApi({
      endpoint: 'api-token.do',
      token: apiToken,
      signature,
      timestamp,
    });

    const host = response.data?.d?.database?.host;

    return NextResponse.json({ host, valid: true, timestamp, signature });
  } catch (err: any) {
    return NextResponse.json(
      {
        valid: false,
        message: err.message,
        status: err.response?.status,
        details: err.response?.data ?? null,
        debug: { timestamp, signature },
      },
      { status: err.response?.status || 500 },
    );
  }
}
