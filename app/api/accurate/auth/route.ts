import { NextRequest, NextResponse } from 'next/server'

export async function GET() {
  try {
    const clientId = process.env.AOL_CLIENT_ID
    const redirectUri = process.env.AOL_REDIRECT_URI
    const scope = process.env.AOL_SCOPE

    if (!clientId || !redirectUri || !scope) {
      return NextResponse.json(
        { error: 'OAuth configuration missing' },
        { status: 500 }
      )
    }

    const params = new URLSearchParams({
      client_id: clientId,
      response_type: 'code',
      redirect_uri: redirectUri,
      scope: scope,
    })

    const authUrl = `https://account.accurate.id/oauth/authorize?${params.toString()}`
    
    console.log('🔐 Redirecting to Accurate OAuth:', authUrl)
    
    return NextResponse.redirect(authUrl)
  } catch (error) {
    console.error('Error in OAuth auth endpoint:', error)
    return NextResponse.json(
      { error: 'Failed to initiate OAuth flow' },
      { status: 500 }
    )
  }
}
