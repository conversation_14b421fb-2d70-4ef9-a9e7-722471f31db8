import { NextRequest, NextResponse } from 'next/server'
import axios from 'axios'

export async function GET(req: NextRequest) {
  try {
    const code = req.nextUrl.searchParams.get('code')
    const error = req.nextUrl.searchParams.get('error')

    if (error) {
      console.error('OAuth error:', error)
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_BASE_URL}/integrations/accurate/error?error=${error}`)
    }

    if (!code) {
      return NextResponse.json({ error: 'Authorization code not found' }, { status: 400 })
    }

    console.log('🔄 Exchanging code for tokens...')

    // Exchange code for tokens
    const basic = Buffer.from(
      `${process.env.AOL_CLIENT_ID}:${process.env.AOL_CLIENT_SECRET}`,
    ).toString('base64')

    const tokenResponse = await axios.post(
      'https://account.accurate.id/oauth/token',
      new URLSearchParams({
        code,
        grant_type: 'authorization_code',
        redirect_uri: process.env.AOL_REDIRECT_URI!,
      }),
      {
        headers: {
          Authorization: `Basic ${basic}`,
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      },
    )

    const tokens = tokenResponse.data
    console.log('✅ Received tokens:', {
      token_type: tokens.token_type,
      scope: tokens.scope,
      expires_in: tokens.expires_in,
      user: tokens.user
    })

    // Get database list
    console.log('🔄 Fetching database list...')
    const dbListResponse = await axios.get(
      'https://account.accurate.id/api/db-list.do',
      {
        headers: {
          Authorization: `Bearer ${tokens.access_token}`
        }
      }
    )

    const databases = dbListResponse.data
    console.log('📊 Available databases:', databases)

    // Extract database array from response
    const dbArray = databases.d || databases

    if (!dbArray || dbArray.length === 0) {
      throw new Error('No databases available')
    }

    // Use first database or find specific one
    const selectedDb = dbArray[0] // You can add logic to select specific database

    console.log('🎯 Selected database:', selectedDb)

    // Open database to get host and session
    console.log('🔄 Opening database:', selectedDb.alias)
    const openDbResponse = await axios.get(
      `https://account.accurate.id/api/open-db.do?id=${selectedDb.id}`,
      {
        headers: {
          Authorization: `Bearer ${tokens.access_token}`
        }
      }
    )

    const dbSession = openDbResponse.data
    console.log('🏠 Database session:', {
      host: dbSession.host,
      session: dbSession.session
    })

    // Save tokens with database info using direct Directus API
    const directusUrl = process.env.NEXT_PUBLIC_DIRECTUS_URL
    const directusToken = process.env.NEXT_PUBLIC_DIRECTUS_TOKEN

    const tokenData = {
      access_token: tokens.access_token,
      refresh_token: tokens.refresh_token,
      token_type: tokens.token_type,
      scope: tokens.scope,
      host: dbSession.host,
      session: dbSession.session,
      db_id: selectedDb.id.toString(),
      db_alias: selectedDb.alias,
      user_id: tokens.user?.id?.toString(),
      expires_at: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(), // 15 days
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    const saveResponse = await fetch(`${directusUrl}/items/AccurateOAuthTokens`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${directusToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(tokenData)
    })

    if (!saveResponse.ok) {
      const error = await saveResponse.text()
      console.error('❌ Failed to save tokens:', error)
      throw new Error('Failed to save OAuth tokens')
    }

    const savedTokens = await saveResponse.json()
    console.log('💾 Tokens saved successfully:', savedTokens.data?.id)

    // Redirect to success page
    return NextResponse.redirect(`${process.env.NEXT_PUBLIC_BASE_URL}/integrations/accurate/success`)

  } catch (error: any) {
    console.error('❌ OAuth callback error:', error.response?.data || error.message)
    return NextResponse.redirect(`${process.env.NEXT_PUBLIC_BASE_URL}/integrations/accurate/error?error=callback_failed`)
  }
}
