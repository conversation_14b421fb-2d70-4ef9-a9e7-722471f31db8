import { NextResponse } from 'next/server';

export async function GET(req: Request) {
  // Check for environment variables but don't expose their values
  const envCheck = {
    ACCURATE_CLIENT_ID: !!process.env.ACCURATE_CLIENT_ID,
    ACCURATE_CLIENT_SECRET: !!process.env.ACCURATE_CLIENT_SECRET,
    ACCURATE_SIGNATURE_SECRET: !!process.env.ACCURATE_SIGNATURE_SECRET,
    NEXT_PUBLIC_BASE_URL: process.env.NEXT_PUBLIC_BASE_URL || null,
    DIRECTUS_URL: !!process.env.DIRECTUS_URL,
    DIRECTUS_API_TOKEN: !!process.env.DIRECTUS_API_TOKEN,
  };

  return NextResponse.json({
    message: 'Environment variables check',
    environment: process.env.NODE_ENV,
    variables: envCheck,
    timestamp: new Date().toISOString(),
  });
} 