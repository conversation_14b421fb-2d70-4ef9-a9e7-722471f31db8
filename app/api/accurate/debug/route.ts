import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const envVars = {
      NEXT_PUBLIC_DIRECTUS_URL: process.env.NEXT_PUBLIC_DIRECTUS_URL,
      NEXT_PUBLIC_DIRECTUS_TOKEN: process.env.NEXT_PUBLIC_DIRECTUS_TOKEN ? 'SET' : 'NOT_SET',
      DIRECTUS_TOKEN: process.env.DIRECTUS_TOKEN ? 'SET' : 'NOT_SET',
      AOL_CLIENT_ID: process.env.AOL_CLIENT_ID ? 'SET' : 'NOT_SET',
      AOL_CLIENT_SECRET: process.env.AOL_CLIENT_SECRET ? 'SET' : 'NOT_SET',
      AOL_REDIRECT_URI: process.env.AOL_REDIRECT_URI,
      AOL_SCOPE: process.env.AOL_SCOPE,
      NEXT_PUBLIC_BASE_URL: process.env.NEXT_PUBLIC_BASE_URL
    }

    return NextResponse.json({
      message: 'Environment variables debug',
      env: envVars
    })

  } catch (error: any) {
    return NextResponse.json(
      { 
        error: 'Debug failed',
        details: error.message
      },
      { status: 500 }
    )
  }
}
