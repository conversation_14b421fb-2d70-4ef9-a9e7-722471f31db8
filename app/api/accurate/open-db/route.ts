import { NextResponse } from 'next/server';
import { format } from 'date-fns-tz';
import crypto from 'crypto';
import { callAccurateApi } from '../accurateApi';

export async function POST(req: Request) {
  const timestamp = format(new Date(), "yyyy-MM-dd'T'HH:mm:ss.SSSXXX", {
    timeZone: 'Asia/Jakarta',
  });
  let signature: string | undefined = undefined;

  try {
    const { access_token } = await req.json();

    if (!access_token) {
      return NextResponse.json({ error: 'Access token is required' }, { status: 400 });
    }

    const secret = process.env.ACCURATE_SIGNATURE_SECRET;
    if (!secret) {
      return NextResponse.json({ error: 'Missing ACCURATE_SIGNATURE_SECRET' }, { status: 500 });
    }

    signature = crypto.createHmac('sha256', secret).update(timestamp).digest('hex');

    const response = await callAccurate<PERSON><PERSON>({
      endpoint: 'open-db.do',
      token: access_token,
      signature,
      timestamp,
    });

    return NextResponse.json(response.data);
  } catch (err: any) {
    return NextResponse.json({ error: err.response?.data || err.message, timestamp, signature }, { status: 500 });
  }
} 