import { NextRequest, NextResponse } from 'next/server'
import axios from 'axios'
import { oauthManager } from '../../../../lib/accurate-oauth'

export async function POST() {
  try {
    console.log('🔄 Refreshing Accurate OAuth tokens...')

    // Get current tokens
    const currentTokens = await oauthManager.getCurrentTokens()
    if (!currentTokens) {
      return NextResponse.json(
        { error: 'No tokens found' },
        { status: 404 }
      )
    }

    // Refresh tokens
    const basic = Buffer.from(
      `${process.env.AOL_CLIENT_ID}:${process.env.AOL_CLIENT_SECRET}`,
    ).toString('base64')

    const refreshResponse = await axios.post(
      'https://account.accurate.id/oauth/token',
      new URLSearchParams({
        grant_type: 'refresh_token',
        refresh_token: currentTokens.refresh_token,
      }),
      { 
        headers: { 
          Authorization: `Basic ${basic}`,
          'Content-Type': 'application/x-www-form-urlencoded'
        } 
      },
    )

    const newTokens = refreshResponse.data
    console.log('✅ Tokens refreshed successfully')

    // Update tokens in database
    const updatedTokens = await oauthManager.updateTokens(currentTokens.id!, {
      access_token: newTokens.access_token,
      refresh_token: newTokens.refresh_token || currentTokens.refresh_token, // Some providers don't return new refresh token
      token_type: newTokens.token_type,
      scope: newTokens.scope
    })

    return NextResponse.json({
      success: true,
      message: 'Tokens refreshed successfully',
      expires_at: updatedTokens.expires_at
    })

  } catch (error: any) {
    console.error('❌ Token refresh error:', error.response?.data || error.message)
    return NextResponse.json(
      { 
        error: 'Failed to refresh tokens',
        details: error.response?.data || error.message
      },
      { status: 500 }
    )
  }
}
