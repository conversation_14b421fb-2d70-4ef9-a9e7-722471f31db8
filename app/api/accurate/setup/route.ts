import { NextResponse } from 'next/server'

export async function POST() {
  try {
    console.log('🔧 Setting up AccurateOAuthTokens table...')
    
    const directusUrl = process.env.NEXT_PUBLIC_DIRECTUS_URL
    const directusToken = process.env.NEXT_PUBLIC_DIRECTUS_TOKEN
    
    if (!directusUrl || !directusToken) {
      return NextResponse.json(
        { error: 'Directus configuration missing' },
        { status: 500 }
      )
    }

    // Create AccurateOAuthTokens collection
    const createCollectionResponse = await fetch(`${directusUrl}/collections`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${directusToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        collection: 'AccurateOAuthTokens',
        meta: {
          collection: 'AccurateOAuthTokens',
          icon: 'key',
          note: 'OAuth tokens for Accurate Online integration',
          display_template: '{{access_token}}',
          hidden: false,
          singleton: false,
          translations: null,
          archive_field: null,
          archive_app_filter: true,
          archive_value: null,
          unarchive_value: null,
          sort_field: null,
          accountability: 'all',
          color: null,
          item_duplication_fields: null,
          sort: null,
          group: null,
          collapse: 'open'
        },
        schema: {
          name: 'AccurateOAuthTokens'
        }
      })
    })

    if (!createCollectionResponse.ok) {
      const error = await createCollectionResponse.text()
      console.log('Collection creation response:', error)
      // Collection might already exist, continue with fields
    }

    // Define fields to create
    const fields = [
      {
        field: 'id',
        type: 'uuid',
        meta: {
          field: 'id',
          special: ['uuid'],
          interface: 'input',
          options: null,
          display: null,
          display_options: null,
          readonly: true,
          hidden: true,
          sort: 1,
          width: 'full',
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null
        },
        schema: {
          name: 'id',
          table: 'AccurateOAuthTokens',
          data_type: 'uuid',
          default_value: null,
          max_length: null,
          numeric_precision: null,
          numeric_scale: null,
          is_nullable: false,
          is_unique: true,
          is_primary_key: true,
          is_generated: false,
          generation_expression: null,
          has_auto_increment: false,
          foreign_key_column: null,
          foreign_key_table: null
        }
      },
      {
        field: 'access_token',
        type: 'text',
        meta: {
          field: 'access_token',
          special: null,
          interface: 'input',
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 2,
          width: 'full',
          translations: null,
          note: 'OAuth access token',
          conditions: null,
          required: true,
          group: null,
          validation: null,
          validation_message: null
        }
      },
      {
        field: 'refresh_token',
        type: 'text',
        meta: {
          field: 'refresh_token',
          special: null,
          interface: 'input',
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 3,
          width: 'full',
          translations: null,
          note: 'OAuth refresh token',
          conditions: null,
          required: true,
          group: null,
          validation: null,
          validation_message: null
        }
      },
      {
        field: 'token_type',
        type: 'string',
        meta: {
          field: 'token_type',
          special: null,
          interface: 'input',
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 4,
          width: 'half',
          translations: null,
          note: 'Token type (usually Bearer)',
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null
        }
      },
      {
        field: 'scope',
        type: 'string',
        meta: {
          field: 'scope',
          special: null,
          interface: 'input',
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 5,
          width: 'half',
          translations: null,
          note: 'OAuth scope permissions',
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null
        }
      },
      {
        field: 'expires_at',
        type: 'timestamp',
        meta: {
          field: 'expires_at',
          special: null,
          interface: 'datetime',
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 6,
          width: 'half',
          translations: null,
          note: 'Token expiration time',
          conditions: null,
          required: true,
          group: null,
          validation: null,
          validation_message: null
        }
      },
      {
        field: 'host',
        type: 'string',
        meta: {
          field: 'host',
          special: null,
          interface: 'input',
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 7,
          width: 'half',
          translations: null,
          note: 'Accurate API host URL',
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null
        }
      },
      {
        field: 'session',
        type: 'string',
        meta: {
          field: 'session',
          special: null,
          interface: 'input',
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 8,
          width: 'half',
          translations: null,
          note: 'Accurate session ID',
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null
        }
      },
      {
        field: 'db_id',
        type: 'string',
        meta: {
          field: 'db_id',
          special: null,
          interface: 'input',
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 9,
          width: 'half',
          translations: null,
          note: 'Accurate database ID',
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null
        }
      },
      {
        field: 'db_alias',
        type: 'string',
        meta: {
          field: 'db_alias',
          special: null,
          interface: 'input',
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 10,
          width: 'half',
          translations: null,
          note: 'Accurate database alias/name',
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null
        }
      },
      {
        field: 'user_id',
        type: 'string',
        meta: {
          field: 'user_id',
          special: null,
          interface: 'input',
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 11,
          width: 'half',
          translations: null,
          note: 'Accurate user ID',
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null
        }
      },
      {
        field: 'created_at',
        type: 'timestamp',
        meta: {
          field: 'created_at',
          special: ['date-created'],
          interface: 'datetime',
          options: null,
          display: null,
          display_options: null,
          readonly: true,
          hidden: true,
          sort: 12,
          width: 'half',
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null
        }
      },
      {
        field: 'updated_at',
        type: 'timestamp',
        meta: {
          field: 'updated_at',
          special: ['date-updated'],
          interface: 'datetime',
          options: null,
          display: null,
          display_options: null,
          readonly: true,
          hidden: true,
          sort: 13,
          width: 'half',
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null
        }
      }
    ]

    // Create fields
    const fieldResults = []
    for (const field of fields) {
      try {
        const fieldResponse = await fetch(`${directusUrl}/fields/AccurateOAuthTokens`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${directusToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(field)
        })

        if (fieldResponse.ok) {
          fieldResults.push({ field: field.field, status: 'created' })
        } else {
          const error = await fieldResponse.text()
          fieldResults.push({ field: field.field, status: 'error', error })
        }
      } catch (error) {
        fieldResults.push({ field: field.field, status: 'error', error: error.message })
      }
    }

    console.log('✅ AccurateOAuthTokens table setup completed')

    return NextResponse.json({
      success: true,
      message: 'AccurateOAuthTokens table setup completed',
      fieldResults
    })

  } catch (error: any) {
    console.error('❌ Setup error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to setup AccurateOAuthTokens table',
        details: error.message
      },
      { status: 500 }
    )
  }
}
