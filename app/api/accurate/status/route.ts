import { NextResponse } from 'next/server'
import { oauthManager } from '../../../../lib/accurate-oauth'

export async function GET() {
  try {
    const tokens = await oauthManager.getCurrentTokens()
    
    if (!tokens) {
      return NextResponse.json(
        { 
          authenticated: false,
          message: 'No OAuth tokens found'
        },
        { status: 404 }
      )
    }

    const needsRefresh = await oauthManager.needsRefresh()
    
    return NextResponse.json({
      authenticated: true,
      db_alias: tokens.db_alias,
      host: tokens.host,
      scope: tokens.scope,
      expires_at: tokens.expires_at,
      needs_refresh: needsRefresh,
      token_type: tokens.token_type
    })
    
  } catch (error: any) {
    console.error('Error checking OAuth status:', error)
    return NextResponse.json(
      { 
        error: 'Failed to check OAuth status',
        details: error.message
      },
      { status: 500 }
    )
  }
}
