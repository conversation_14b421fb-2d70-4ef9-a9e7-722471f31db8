import { NextResponse } from 'next/server';
import axios from 'axios';
import { format } from 'date-fns-tz';

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { access_token, sessionId, host } = body;

    if (!access_token || !sessionId) {
      return NextResponse.json({ error: 'Access token and sessionId are required' }, { status: 400 });
    }

    const directusUrl = process.env.DIRECTUS_URL || 'https://your-directus-instance.com';
    const token = process.env.DIRECTUS_API_TOKEN;

    // Generate UTC timestamp in the required format
    const timestamp = format(new Date(), 'dd/MM/yyyy HH:mm:ss', { timeZone: 'UTC' });

    // Get unsynchronized stock movements
    const stockRes = await axios.get(`${directusUrl}/items/stock_movement`, {
      headers: { Authorization: `Bearer ${token}` },
      params: {
        filter: {
          type: { _eq: 'incoming' },
          synced_to_accurate: { _neq: true },
        },
        fields: 'id,quantity,date,reference_number,purchase_price,produk_gudang_id.id,produk_gudang_id.sku,produk_gudang_id.name,produk_gudang_id.unit',
        limit: -1,
      },
    });

    const items = stockRes.data.data;
    if (items.length === 0) {
      return NextResponse.json({ message: 'No data needs to be synchronized.' });
    }

    // Map items for Accurate API
    const detailItem = items.map((item: any) => ({
      itemNo: item.produk_gudang_id.sku,
      quantity: item.quantity,
      unit1Name: item.produk_gudang_id.unit || 'PCS',
      detailName: item.produk_gudang_id.name,
      unitPrice: item.purchase_price || 0,
    }));

    // Send to Accurate
    const payload = {
      transDate: new Date().toISOString().split('T')[0],
      vendorName: 'Import Directus',
      detailItem,
    };

    // Use the provided host if available, otherwise default to 'accurate.id'
    const apiHost = host ? `https://${host}.accurate.id` : 'https://accurate.id';

    const invoice = await axios.post(`${apiHost}/api/purchase-invoice/save.do`, payload, {
      headers: {
        Authorization: `Bearer ${access_token}`,
        'X-Session-ID': sessionId,
        'X-Api-Timestamp': timestamp,
        'Content-Type': 'application/json',
      },
    });

    // Mark data as synchronized
    await Promise.all(
      items.map((item: any) =>
        axios.patch(`${directusUrl}/items/stock_movement/${item.id}`, {
          synced_to_accurate: true,
          synced_at: new Date().toISOString(),
        }, {
          headers: { Authorization: `Bearer ${token}` },
        })
      )
    );

    return NextResponse.json({
      synced: items.length,
      invoice_id: invoice.data.value?.id,
      timestamp,
    });
  } catch (err: any) {
    console.error('Error in sync-incoming-to-purchase:', err);
    return NextResponse.json({ error: err.response?.data || err.message }, { status: 500 });
  }
} 