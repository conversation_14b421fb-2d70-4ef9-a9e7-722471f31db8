import { NextResponse } from 'next/server'
import { oauthManager } from '../../../../lib/accurate-oauth'
import axios from 'axios'

export async function POST() {
  try {
    console.log('🧪 Testing Accurate connection...')
    
    const tokens = await oauthManager.getCurrentTokens()
    if (!tokens) {
      return NextResponse.json(
        { 
          success: false,
          error: 'No OAuth tokens found. Please authenticate first.'
        },
        { status: 401 }
      )
    }

    // Test API call - get item list
    const testUrl = `${tokens.host}/accurate/api/item/list.do`
    
    console.log('🔗 Testing URL:', testUrl)
    
    const response = await axios.get(testUrl, {
      headers: {
        'Authorization': `Bearer ${tokens.access_token}`,
        'X-Session-ID': tokens.session,
      },
      params: {
        sp: 1, // page
        ps: 1  // page size (just get 1 item for test)
      }
    })

    console.log('✅ Test response:', {
      status: response.status,
      statusText: response.statusText,
      dataType: typeof response.data
    })

    // Check if response is valid
    if (response.status === 200 && response.data) {
      return NextResponse.json({
        success: true,
        message: 'Connection test successful',
        response_type: typeof response.data,
        status: response.status
      })
    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid response from Accurate API',
        status: response.status
      })
    }

  } catch (error: any) {
    console.error('❌ Connection test failed:', error.response?.data || error.message)
    
    return NextResponse.json({
      success: false,
      error: error.response?.data?.message || error.message || 'Connection test failed',
      status: error.response?.status || 500
    })
  }
}
