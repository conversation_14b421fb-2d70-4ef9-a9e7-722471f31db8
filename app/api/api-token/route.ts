import { NextResponse } from 'next/server';

export async function POST(req: Request) {
  // Forward the request to the correct endpoint
  const url = new URL(req.url);
  const newUrl = new URL('/api/accurate/api-token', url.origin);
  
  // Create a new request with the same body
  const body = await req.text();
  
  // Return a redirect or forward the request
  return NextResponse.redirect(newUrl, { status: 307 }); // 307 preserves the HTTP method and body
} 