import { NextRequest, NextResponse } from 'next/server'
import { accurateSync } from '@/lib/accurate-sync'

export async function GET(request: NextRequest) {
  try {
    // Verify cron secret to prevent unauthorized access
    const authHeader = request.headers.get('authorization')
    const cronSecret = process.env.CRON_SECRET
    
    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log('🕐 Cron job: Retrying failed syncs')
    
    // Retry failed syncs (limit to 20 per run to avoid timeout)
    const results = await accurateSync.retryFailedSyncs(20)
    
    const summary = {
      timestamp: new Date().toISOString(),
      total: results.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      results: results.map(r => ({
        success: r.success,
        accurateId: r.accurateId,
        accurateNo: r.accurateNo,
        error: r.error
      }))
    }

    console.log('📊 Cron job summary:', summary)

    return NextResponse.json({
      success: true,
      message: 'Retry sync completed',
      ...summary
    })
  } catch (error) {
    console.error('❌ Cron job error:', error)
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  // Allow manual trigger via POST
  return GET(request)
}
