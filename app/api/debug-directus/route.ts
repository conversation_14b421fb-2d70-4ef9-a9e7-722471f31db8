import { NextResponse } from 'next/server'
import { debugDirectus, testCollection } from '@/lib/debug-directus'

export async function GET(request: Request) {
  try {
    // Extract query parameters
    const url = new URL(request.url)
    const collection = url.searchParams.get('collection')
    
    if (collection) {
      // Test specific collection if provided
      const result = await testCollection(collection)
      return NextResponse.json({
        success: true, 
        message: `Tested collection: ${collection}`,
        result
      })
    } else {
      // Run full debug
      await debugDirectus()
      return NextResponse.json({
        success: true,
        message: 'Directus debug complete. Check server logs for details.'
      })
    }
  } catch (error: any) {
    console.error('Error debugging Directus:', error)
    return NextResponse.json(
      {
        success: false,
        message: 'Error debugging Directus',
        error: error.message || String(error)
      },
      { status: 500 }
    )
  }
} 