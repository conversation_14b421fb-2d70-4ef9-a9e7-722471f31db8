import { NextResponse } from 'next/server';
import formidable from 'formidable';
import { read, utils } from 'xlsx';
import fs from 'fs';
import path from 'path';
import os from 'os';
import directus from '@/lib/directus';
import { createItem, readItems } from '@directus/sdk';
import { ProdukGudang, StockMovement } from '@/lib/directus';

export const runtime = "edge";

export const config = {
  api: {
    bodyParser: false,
  },
};

// Helper function for file uploads in App Router
async function parseFormData(req: Request): Promise<{ fields: Record<string, string>; file?: Buffer }> {
  const formData = await req.formData();
  const fields: Record<string, string> = {};
  let file: Buffer | undefined;

  for (const [key, value] of formData.entries()) {
    if (value instanceof File) {
      // Handle file upload
      const buffer = Buffer.from(await value.arrayBuffer());
      file = buffer;
    } else {
      // Handle other form fields
      fields[key] = value.toString();
    }
  }

  return { fields, file };
}

export async function POST(req: Request) {
  try {
    // Parse the form data
    const { file } = await parseFormData(req);
    
    if (!file) {
      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 });
    }
    
    // Read the Excel file from buffer
    const workbook = read(file);
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    const data = utils.sheet_to_json(sheet);

    // Mapping ke schema ProdukGudang
    const mapped = data.map((item: any) => ({
      sku: String(item['Kode Barang'] || '').trim(),
      name: String(item['Nama Barang'] || '').trim(),
      category: String(item['Kategori Barang'] || '').trim(),
      unit: String(item['Satuan'] || '').trim(),
      description: String(item['Jenis Barang'] || '').trim(),
      min_stock: 0,
      stok_awal: Number(item['Stok Awal']) || 0,
      nilai_satuan: item['Nilai Satuanya'] || '',
    }));

    // Get existing SKUs from Directus
    const existingRes = await directus.request(
      readItems('ProdukGudang', {
        fields: ['id', 'sku'],
        limit: -1,
      })
    );

    // Create a map of existing SKUs to their IDs
    const existingMap = new Map(
      existingRes.map((item: any) => [item.sku.trim(), item.id])
    );

    // Filter only new items
    const newItems = mapped.filter((item) => !existingMap.has(item.sku));
    const createdProducts = [];

    // Insert new products one by one to get their IDs
    for (const item of newItems) {
      const productData = {
        sku: item.sku,
        name: item.name,
        category: item.category,
        unit: item.unit,
        description: item.description,
        min_stock: item.min_stock,
      };
      
      const newProduct = await directus.request(createItem('ProdukGudang', productData));
      
      createdProducts.push({
        product_id: newProduct.id,
        sku: item.sku,
        quantity: item.stok_awal,
      });
    }

    // Add stock movements for items with initial stock
    const today = new Date().toISOString().split('T')[0];
    const stockMovements = [];
    
    for (const product of createdProducts) {
      if (product.quantity > 0) {
        const movementData: Partial<StockMovement> = {
          produk_gudang_id: product.product_id,
          quantity: product.quantity,
          date: today,
          type: 'incoming' as 'incoming',
          reference_number: 'Initial Stock',
          notes: 'Import from Excel',
        };
        
        const newMovement = await directus.request(createItem('StockMovement', movementData));
        stockMovements.push(newMovement);
      }
    }

    return NextResponse.json({
      success: true,
      imported_products: createdProducts.length,
      added_stock_movements: stockMovements.length,
      skipped: mapped.length - newItems.length,
    });
  } catch (error: any) {
    console.error('Import error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to import Excel file' },
      { status: 500 }
    );
  }
} 