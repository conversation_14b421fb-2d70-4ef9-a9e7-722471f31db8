import { NextResponse } from 'next/server';
import axios from 'axios';
import { format } from 'date-fns-tz';

export async function GET(req: Request) {
  const url = new URL(req.url);
  const code = url.searchParams.get('code');

  if (!code) {
    return NextResponse.json({ error: 'Authorization code not provided' }, { status: 400 });
  }

  const client_id = process.env.ACCURATE_CLIENT_ID;
  const client_secret = process.env.ACCURATE_CLIENT_SECRET;
  const redirect_uri = `${process.env.NEXT_PUBLIC_BASE_URL}/api/oauth/callback`;

  // Generate UTC timestamp in the required format
  const timestamp = format(new Date(), 'dd/MM/yyyy HH:mm:ss', { timeZone: 'UTC' });

  try {
    const response = await axios.post('https://account.accurate.id/oauth/token', null, {
      params: {
        code,
        client_id,
        client_secret,
        grant_type: 'authorization_code',
        redirect_uri,
      },
      headers: {
        'X-Api-Timestamp': timestamp
      }
    });

    const { access_token, refresh_token } = response.data;

    // Redirect to the Accurate integration page with tokens as URL params
    // In a production app, you might want to store these in a more secure way
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_BASE_URL}/accurate?access_token=${access_token}&refresh_token=${refresh_token}&timestamp=${encodeURIComponent(timestamp)}`
    );
  } catch (err: any) {
    console.error('Error in OAuth callback:', err);
    return NextResponse.json({ error: err.response?.data || err.message, timestamp }, { status: 500 });
  }
} 