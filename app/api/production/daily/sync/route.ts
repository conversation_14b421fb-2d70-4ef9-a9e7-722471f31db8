import { NextRequest, NextResponse } from 'next/server'

const DIRECTUS_URL = process.env.DIRECTUS_URL || 'http://localhost:8055'

export const dynamic = 'force-dynamic'

// POST - Sync daily production to production stock
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const body = await request.json()
    const { daily_production_id } = body

    console.log('🔄 Syncing daily production to stock:', daily_production_id)

    if (!daily_production_id) {
      return NextResponse.json(
        { success: false, error: 'Daily production ID is required' },
        { status: 400 }
      )
    }

    // Get daily production record
    const dailyProductionResponse = await fetch(`${DIRECTUS_URL}/items/DailyProduction/${daily_production_id}`, {
      headers: { 'Authorization': `<PERSON><PERSON> ${token}` }
    })

    if (!dailyProductionResponse.ok) {
      return NextResponse.json(
        { success: false, error: 'Daily production not found' },
        { status: 404 }
      )
    }

    const dailyProductionData = await dailyProductionResponse.json()
    const dailyProduction = dailyProductionData.data

    if (!dailyProduction.quantity_produced || dailyProduction.quantity_produced <= 0) {
      return NextResponse.json(
        { success: false, error: 'No quantity produced to sync' },
        { status: 400 }
      )
    }

    // Check if already synced
    if (dailyProduction.synced_to_stock) {
      return NextResponse.json(
        { success: false, error: 'Daily production already synced to stock' },
        { status: 400 }
      )
    }

    // Add to production stock
    const addStockResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/production/stock`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        produk_id: dailyProduction.produk_id,
        quantity: dailyProduction.quantity_produced,
        notes: `Synced from Daily Production ${daily_production_id} on ${dailyProduction.date}`
      })
    })

    if (!addStockResponse.ok) {
      const errorData = await addStockResponse.json()
      throw new Error(`Failed to add to production stock: ${errorData.error}`)
    }

    // Mark daily production as synced
    const updateDailyProductionResponse = await fetch(`${DIRECTUS_URL}/items/DailyProduction/${daily_production_id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        synced_to_stock: true,
        synced_at: new Date().toISOString()
      })
    })

    if (!updateDailyProductionResponse.ok) {
      console.error('⚠️ Failed to mark daily production as synced, but stock was added')
    }

    console.log('✅ Daily production synced to stock successfully')

    return NextResponse.json({
      success: true,
      message: `Successfully synced ${dailyProduction.quantity_produced} units to production stock`,
      data: {
        daily_production_id,
        quantity_synced: dailyProduction.quantity_produced,
        produk_id: dailyProduction.produk_id
      }
    })

  } catch (error) {
    console.error('❌ Error syncing daily production to stock:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to sync daily production to stock',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// GET - Check sync status for daily productions
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const { searchParams } = new URL(request.url)
    
    const date = searchParams.get('date') || new Date().toISOString().split('T')[0]

    console.log('🔄 Checking sync status for date:', date)

    // Get daily productions for the date
    const dailyProductionsResponse = await fetch(`${DIRECTUS_URL}/items/DailyProduction?filter[date][_eq]=${date}&fields=*,produk.*`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!dailyProductionsResponse.ok) {
      throw new Error('Failed to fetch daily productions')
    }

    const dailyProductionsData = await dailyProductionsResponse.json()
    const dailyProductions = dailyProductionsData.data || []

    // Categorize productions
    const syncStatus = {
      total: dailyProductions.length,
      synced: dailyProductions.filter((dp: any) => dp.synced_to_stock).length,
      unsynced: dailyProductions.filter((dp: any) => !dp.synced_to_stock && dp.quantity_produced > 0).length,
      no_production: dailyProductions.filter((dp: any) => !dp.quantity_produced || dp.quantity_produced <= 0).length,
      productions: dailyProductions.map((dp: any) => ({
        id: dp.id,
        produk_id: dp.produk_id,
        produk_name: dp.produk?.nama_produk || 'Unknown Product',
        quantity_produced: dp.quantity_produced || 0,
        synced_to_stock: dp.synced_to_stock || false,
        synced_at: dp.synced_at,
        can_sync: !dp.synced_to_stock && dp.quantity_produced > 0
      }))
    }

    return NextResponse.json({
      success: true,
      data: syncStatus
    })

  } catch (error) {
    console.error('❌ Error checking sync status:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to check sync status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
