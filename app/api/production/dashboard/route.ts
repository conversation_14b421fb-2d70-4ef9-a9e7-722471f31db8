import { NextRequest, NextResponse } from 'next/server'

const DIRECTUS_URL = process.env.DIRECTUS_URL || 'http://localhost:8055'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    // Validate authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const today = new Date().toISOString().split('T')[0]

    console.log('🔄 Fetching production dashboard data for:', today)

    // Fetch today's production data
    const productionResponse = await fetch(`${DIRECTUS_URL}/items/DailyProduction?filter[date][_eq]=${today}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    // Fetch production stock data
    const stockResponse = await fetch(`${DIRECTUS_URL}/items/production_stock`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    // Fetch today's distributions
    const distributionsResponse = await fetch(`${DIRECTUS_URL}/items/production_distributions?filter[date][_eq]=${today}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    // Fetch products for reference
    const productsResponse = await fetch(`${DIRECTUS_URL}/items/produk`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!productionResponse.ok || !stockResponse.ok || !distributionsResponse.ok || !productsResponse.ok) {
      throw new Error('Failed to fetch data from Directus')
    }

    const [productionData, stockData, distributionsData, productsData] = await Promise.all([
      productionResponse.json(),
      stockResponse.json(),
      distributionsResponse.json(),
      productsResponse.json()
    ])

    const productions = productionData.data || []
    const stocks = stockData.data || []
    const distributions = distributionsData.data || []
    const products = productsData.data || []

    // Calculate summary metrics
    const totalProduced = productions.reduce((sum: number, prod: any) => sum + (prod.quantity_produced || 0), 0)
    const totalAvailableStock = stocks.reduce((sum: number, stock: any) => sum + (stock.available_quantity || 0), 0)
    const totalDistributed = distributions
      .filter((d: any) => d.status === 'distributed')
      .reduce((sum: number, dist: any) => sum + (dist.quantity || 0), 0)
    const totalReturned = distributions
      .reduce((sum: number, dist: any) => sum + (dist.returned_quantity || 0), 0)

    // Build dashboard data
    const dashboardData = {
      today: {
        date: today,
        total_produced: totalProduced,
        total_distributed: totalDistributed,
        total_returned: totalReturned,
        total_available: totalAvailableStock,
        products: productions.map((prod: any) => {
          const product = products.find((p: any) => p.id === prod.produk_id)
          const productDistributions = distributions.filter((d: any) => d.produk_id === prod.produk_id)
          
          return {
            produk_id: prod.produk_id,
            nama_produk: product?.nama_produk || 'Unknown Product',
            produced: prod.quantity_produced || 0,
            distributed: productDistributions
              .filter((d: any) => d.status === 'distributed')
              .reduce((sum: number, d: any) => sum + (d.quantity || 0), 0),
            returned: productDistributions
              .reduce((sum: number, d: any) => sum + (d.returned_quantity || 0), 0),
            available: stocks.find((s: any) => s.produk_id === prod.produk_id)?.available_quantity || 0,
            distributions: productDistributions.map((d: any) => ({
              channel_name: d.distribution_channel_id, // This should be populated with actual channel name
              channel_type: 'unknown', // This should be populated with actual channel type
              quantity: d.quantity || 0,
              status: d.status || 'unknown'
            }))
          }
        })
      },
      week: {
        period: 'weekly',
        start_date: today,
        end_date: today,
        total_production: totalProduced,
        total_distribution: totalDistributed,
        total_returns: totalReturned,
        return_rate: totalDistributed > 0 ? (totalReturned / totalDistributed) * 100 : 0,
        top_products: [],
        channel_performance: []
      },
      month: {
        period: 'monthly',
        start_date: today,
        end_date: today,
        total_production: totalProduced,
        total_distribution: totalDistributed,
        total_returns: totalReturned,
        return_rate: totalDistributed > 0 ? (totalReturned / totalDistributed) * 100 : 0,
        top_products: [],
        channel_performance: []
      },
      alerts: {
        low_stock: stocks
          .filter((stock: any) => (stock.available_quantity || 0) < 10)
          .map((stock: any) => {
            const product = products.find((p: any) => p.id === stock.produk_id)
            return {
              produk_id: stock.produk_id,
              nama_produk: product?.nama_produk || 'Unknown Product',
              quantity: stock.available_quantity || 0
            }
          }),
        high_returns: [],
        production_targets: productions
          .filter((prod: any) => prod.target_quantity && prod.quantity_produced)
          .map((prod: any) => {
            const product = products.find((p: any) => p.id === prod.produk_id)
            const variance = prod.target_quantity > 0 ? 
              ((prod.quantity_produced - prod.target_quantity) / prod.target_quantity) * 100 : 0
            
            return {
              produk_id: prod.produk_id,
              nama_produk: product?.nama_produk || 'Unknown Product',
              target: prod.target_quantity,
              actual: prod.quantity_produced,
              variance: Math.round(variance)
            }
          })
      }
    }

    console.log('✅ Dashboard data compiled successfully')

    return NextResponse.json({
      success: true,
      data: dashboardData
    })

  } catch (error) {
    console.error('❌ Error fetching production dashboard data:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch dashboard data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
