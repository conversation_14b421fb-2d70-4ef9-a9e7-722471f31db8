import { NextRequest, NextResponse } from 'next/server'

const DIRECTUS_URL = process.env.DIRECTUS_URL || 'http://localhost:8055'

export const dynamic = 'force-dynamic'

// GET - Fetch sales plans with filters
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const { searchParams } = new URL(request.url)
    
    const status = searchParams.get('status')
    const kangider = searchParams.get('kangider')
    const date = searchParams.get('date')

    // Build filter
    const filters = []
    if (status && status !== 'all') filters.push(`filter[status][_eq]=${status}`)
    if (kangider && kangider !== 'all') filters.push(`filter[kangider][_eq]=${kangider}`)
    if (date) filters.push(`filter[date][_eq]=${date}`)

    const filterQuery = filters.length > 0 ? `?${filters.join('&')}` : ''

    console.log('🔄 Fetching sales plans with filters:', filterQuery)

    // Fetch sales plans with items
    const response = await fetch(`${DIRECTUS_URL}/items/sales_planned${filterQuery}&fields=*,items.*`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!response.ok) {
      throw new Error(`Directus API error: ${response.status}`)
    }

    const data = await response.json()
    
    return NextResponse.json({
      success: true,
      data: data.data || []
    })

  } catch (error) {
    console.error('❌ Error fetching sales plans:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch sales plans',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// POST - Process "load ider" workflow
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const body = await request.json()
    const { sales_plan_id, action } = body

    console.log('🔄 Processing Kang Ider workflow:', { sales_plan_id, action })

    if (!sales_plan_id || !action) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      )
    }

    if (action === 'load_ider') {
      // Get sales plan with items
      const salesPlanResponse = await fetch(`${DIRECTUS_URL}/items/sales_planned/${sales_plan_id}?fields=*,items.*`, {
        headers: { 'Authorization': `Bearer ${token}` }
      })

      if (!salesPlanResponse.ok) {
        return NextResponse.json(
          { success: false, error: 'Sales plan not found' },
          { status: 404 }
        )
      }

      const salesPlanData = await salesPlanResponse.json()
      const salesPlan = salesPlanData.data

      if (!salesPlan.items || salesPlan.items.length === 0) {
        return NextResponse.json(
          { success: false, error: 'Sales plan has no items' },
          { status: 400 }
        )
      }

      // Update sales plan status to "load ider"
      const updateResponse = await fetch(`${DIRECTUS_URL}/items/sales_planned/${sales_plan_id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          status: 'load ider',
          date_updated: new Date().toISOString()
        })
      })

      if (!updateResponse.ok) {
        throw new Error('Failed to update sales plan status')
      }

      // Create kas_harian record
      const kasHarianData = {
        status: 'published',
        tanggal: salesPlan.date,
        idkangider: salesPlan.kangider,
        stok_awal: salesPlan.total_cup,
        stock_akhir: salesPlan.total_cup,
        keterangan: `Created from Sales Plan ${sales_plan_id}`
      }

      const kasHarianResponse = await fetch(`${DIRECTUS_URL}/items/kas_harian`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(kasHarianData)
      })

      if (!kasHarianResponse.ok) {
        const errorData = await kasHarianResponse.json()
        throw new Error(`Failed to create kas_harian: ${errorData.errors?.[0]?.message || 'Unknown error'}`)
      }

      const kasHarian = await kasHarianResponse.json()

      // Create stock_harian records for each item
      const stockHarianRecords = []
      for (const item of salesPlan.items) {
        const stockHarianData = {
          id_produk: item.produk,
          id_kangider: salesPlan.kangider,
          tanggal_stock: salesPlan.date,
          stock_awal: item.quantity,
          stock_akhir: item.quantity,
          status: 'published',
          kas_harian: kasHarian.data.id
        }

        const stockHarianResponse = await fetch(`${DIRECTUS_URL}/items/stock_harian`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(stockHarianData)
        })

        if (stockHarianResponse.ok) {
          const stockHarian = await stockHarianResponse.json()
          stockHarianRecords.push(stockHarian.data)
        } else {
          console.error('Failed to create stock_harian for item:', item)
        }
      }

      // Reduce production stock for each item
      for (const item of salesPlan.items) {
        if (item.quantity <= 0) continue

        // Get current production stock
        const stockResponse = await fetch(`${DIRECTUS_URL}/items/production_stock?filter[produk][_eq]=${item.produk}`, {
          headers: { 'Authorization': `Bearer ${token}` }
        })

        if (stockResponse.ok) {
          const stockData = await stockResponse.json()
          const currentStock = stockData.data?.[0]

          if (currentStock) {
            const newQuantity = Math.max(0, currentStock.quantity - item.quantity)

            // Update production stock
            await fetch(`${DIRECTUS_URL}/items/production_stock/${currentStock.id}`, {
              method: 'PATCH',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
              },
              body: JSON.stringify({
                quantity: newQuantity,
                last_updated: new Date().toISOString()
              })
            })

            // Record stock movement
            await fetch(`${DIRECTUS_URL}/items/stock_movement`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
              },
              body: JSON.stringify({
                produk: item.produk,
                quantity: item.quantity,
                type: 'outgoing',
                date: new Date().toISOString(),
                reference: `Sales Plan Load Ider (${sales_plan_id})`,
                notes: 'Stock reduced for Kang Ider loading'
              })
            })

            console.log(`✅ Stock for product ${item.produk} reduced by ${item.quantity}`)
          }
        }
      }

      console.log('✅ Load ider workflow completed successfully')

      return NextResponse.json({
        success: true,
        data: {
          salesPlan: salesPlan,
          kasHarian: kasHarian.data,
          stockHarianRecords,
          message: `Load ider processed successfully. Created kas_harian and ${stockHarianRecords.length} stock_harian records.`
        }
      })
    }

    return NextResponse.json(
      { success: false, error: 'Invalid action' },
      { status: 400 }
    )

  } catch (error) {
    console.error('❌ Error processing Kang Ider workflow:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to process workflow',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// PATCH - Update sales plan status
export async function PATCH(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const body = await request.json()
    const { id, status } = body

    console.log('🔄 Updating sales plan status:', { id, status })

    if (!id || !status) {
      return NextResponse.json(
        { success: false, error: 'Sales plan ID and status are required' },
        { status: 400 }
      )
    }

    const updateData = {
      status,
      date_updated: new Date().toISOString()
    }

    const response = await fetch(`${DIRECTUS_URL}/items/sales_planned/${id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(updateData)
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(`Failed to update sales plan: ${errorData.errors?.[0]?.message || 'Unknown error'}`)
    }

    const updatedSalesPlan = await response.json()

    console.log('✅ Sales plan status updated successfully')

    return NextResponse.json({
      success: true,
      data: updatedSalesPlan.data,
      message: 'Sales plan status updated successfully'
    })

  } catch (error) {
    console.error('❌ Error updating sales plan:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update sales plan',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
