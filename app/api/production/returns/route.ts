import { NextRequest, NextResponse } from 'next/server'

const DIRECTUS_URL = process.env.DIRECTUS_URL || 'http://localhost:8055'

export const dynamic = 'force-dynamic'

// GET - Fetch returns
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const { searchParams } = new URL(request.url)
    
    const distribution_id = searchParams.get('distribution_id')
    const date = searchParams.get('date')

    // Build filter
    const filters = []
    if (distribution_id) filters.push(`filter[distribution_id][_eq]=${distribution_id}`)
    if (date) filters.push(`filter[returned_at][_gte]=${date}`)

    const filterQuery = filters.length > 0 ? `?${filters.join('&')}` : ''

    console.log('🔄 Fetching returns with filters:', filterQuery)

    const response = await fetch(`${DIRECTUS_URL}/items/production_returns${filterQuery}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!response.ok) {
      throw new Error(`Directus API error: ${response.status}`)
    }

    const data = await response.json()
    
    return NextResponse.json({
      success: true,
      data: data.data || []
    })

  } catch (error) {
    console.error('❌ Error fetching returns:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch returns',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// POST - Create return
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const body = await request.json()
    const { distribution_id, quantity, reason, condition, action, notes } = body

    console.log('🔄 Processing return for distribution:', distribution_id)

    if (!distribution_id || !quantity || !reason || !condition || !action) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Get distribution info
    const distributionResponse = await fetch(`${DIRECTUS_URL}/items/production_distributions/${distribution_id}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!distributionResponse.ok) {
      return NextResponse.json(
        { success: false, error: 'Distribution not found' },
        { status: 404 }
      )
    }

    const distributionData = await distributionResponse.json()
    const distribution = distributionData.data

    // Validate return quantity
    const maxReturnQuantity = distribution.quantity - (distribution.returned_quantity || 0)
    if (quantity > maxReturnQuantity) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Cannot return ${quantity} items. Maximum returnable: ${maxReturnQuantity}` 
        },
        { status: 400 }
      )
    }

    // Create return record
    const returnData = {
      distribution_id,
      produk_id: distribution.produk_id,
      quantity,
      reason,
      condition,
      action,
      returned_at: new Date().toISOString(),
      notes: notes || null
    }

    const createReturnResponse = await fetch(`${DIRECTUS_URL}/items/production_returns`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(returnData)
    })

    if (!createReturnResponse.ok) {
      const errorData = await createReturnResponse.json()
      throw new Error(`Failed to create return: ${errorData.errors?.[0]?.message || 'Unknown error'}`)
    }

    const createdReturn = await createReturnResponse.json()

    // Update distribution with return info
    const newReturnedQuantity = (distribution.returned_quantity || 0) + quantity
    const newStatus = newReturnedQuantity >= distribution.quantity ? 'returned' : 'partial'

    const updateDistributionData = {
      returned_quantity: newReturnedQuantity,
      status: newStatus,
      updated_at: new Date().toISOString()
    }

    const updateDistributionResponse = await fetch(`${DIRECTUS_URL}/items/production_distributions/${distribution_id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(updateDistributionData)
    })

    if (!updateDistributionResponse.ok) {
      console.error('⚠️ Failed to update distribution, but return was created')
    }

    // If action is restock and condition is good, add back to production stock
    if (action === 'restock' && condition === 'good') {
      try {
        // Get current stock
        const stockResponse = await fetch(`${DIRECTUS_URL}/items/production_stock?filter[produk_id][_eq]=${distribution.produk_id}`, {
          headers: { 'Authorization': `Bearer ${token}` }
        })

        if (stockResponse.ok) {
          const stockData = await stockResponse.json()
          const stock = stockData.data?.[0]

          if (stock) {
            // Update stock quantities
            const updatedStock = {
              quantity: stock.quantity + quantity,
              reserved_quantity: Math.max(0, stock.reserved_quantity - quantity),
              available_quantity: stock.available_quantity + quantity,
              last_updated: new Date().toISOString()
            }

            const updateStockResponse = await fetch(`${DIRECTUS_URL}/items/production_stock/${stock.id}`, {
              method: 'PATCH',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
              },
              body: JSON.stringify(updatedStock)
            })

            if (updateStockResponse.ok) {
              console.log('✅ Stock updated successfully for restock action')
            } else {
              console.error('⚠️ Failed to update stock for restock action')
            }
          } else {
            // Create new stock record if none exists
            const newStock = {
              produk_id: distribution.produk_id,
              quantity,
              reserved_quantity: 0,
              available_quantity: quantity,
              last_updated: new Date().toISOString()
            }

            const createStockResponse = await fetch(`${DIRECTUS_URL}/items/production_stock`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
              },
              body: JSON.stringify(newStock)
            })

            if (createStockResponse.ok) {
              console.log('✅ New stock record created for restock action')
            } else {
              console.error('⚠️ Failed to create stock record for restock action')
            }
          }
        }
      } catch (stockError) {
        console.error('⚠️ Error handling restock action:', stockError)
      }
    }

    console.log('✅ Return processed successfully')

    return NextResponse.json({
      success: true,
      data: createdReturn.data,
      message: 'Return processed successfully'
    })

  } catch (error) {
    console.error('❌ Error processing return:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to process return',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
