import { NextRequest, NextResponse } from 'next/server'

const DIRECTUS_URL = process.env.DIRECTUS_URL || 'http://localhost:8055'

export const dynamic = 'force-dynamic'

// GET - Fetch production stock
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const { searchParams } = new URL(request.url)
    
    const produk_id = searchParams.get('produk_id')

    // Build filter
    const filters = []
    if (produk_id) filters.push(`filter[produk][_eq]=${produk_id}`)

    const filterQuery = filters.length > 0 ? `?${filters.join('&')}` : ''

    console.log('🔄 Fetching production stock:', filterQuery)

    // Fetch production stock with product details
    const response = await fetch(`${DIRECTUS_URL}/items/production_stock${filterQuery}&fields=*,produk.*`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!response.ok) {
      throw new Error(`Directus API error: ${response.status}`)
    }

    const data = await response.json()
    
    return NextResponse.json({
      success: true,
      data: data.data || []
    })

  } catch (error) {
    console.error('❌ Error fetching production stock:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch production stock',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// POST - Add production stock (from daily production)
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const body = await request.json()
    const { produk_id, quantity, notes } = body

    console.log('🔄 Adding production stock:', { produk_id, quantity })

    if (!produk_id || !quantity || quantity <= 0) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data' },
        { status: 400 }
      )
    }

    // Check if production stock already exists for this product
    const existingStockResponse = await fetch(`${DIRECTUS_URL}/items/production_stock?filter[produk][_eq]=${produk_id}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!existingStockResponse.ok) {
      throw new Error('Failed to check existing stock')
    }

    const existingStockData = await existingStockResponse.json()
    const existingStock = existingStockData.data?.[0]

    if (existingStock) {
      // Update existing stock
      const newQuantity = existingStock.quantity + quantity
      const newAvailable = newQuantity - existingStock.reserved_quantity

      const updateData = {
        quantity: newQuantity,
        available_quantity: newAvailable,
        last_updated: new Date().toISOString()
      }

      const updateResponse = await fetch(`${DIRECTUS_URL}/items/production_stock/${existingStock.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(updateData)
      })

      if (!updateResponse.ok) {
        const errorData = await updateResponse.json()
        throw new Error(`Failed to update stock: ${errorData.errors?.[0]?.message || 'Unknown error'}`)
      }

      const updatedStock = await updateResponse.json()

      // Record stock movement
      await fetch(`${DIRECTUS_URL}/items/stock_movement`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          produk: produk_id,
          quantity: quantity,
          type: 'incoming',
          date: new Date().toISOString(),
          reference: 'Production Stock Addition',
          notes: notes || 'Added from daily production'
        })
      })

      console.log('✅ Production stock updated successfully')

      return NextResponse.json({
        success: true,
        data: updatedStock.data,
        message: 'Production stock updated successfully'
      })
    } else {
      // Create new stock record
      const newStockData = {
        produk: produk_id,
        quantity: quantity,
        reserved_quantity: 0,
        available_quantity: quantity,
        last_updated: new Date().toISOString()
      }

      const createResponse = await fetch(`${DIRECTUS_URL}/items/production_stock`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(newStockData)
      })

      if (!createResponse.ok) {
        const errorData = await createResponse.json()
        throw new Error(`Failed to create stock: ${errorData.errors?.[0]?.message || 'Unknown error'}`)
      }

      const newStock = await createResponse.json()

      // Record stock movement
      await fetch(`${DIRECTUS_URL}/items/stock_movement`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          produk: produk_id,
          quantity: quantity,
          type: 'incoming',
          date: new Date().toISOString(),
          reference: 'Production Stock Creation',
          notes: notes || 'Initial production stock'
        })
      })

      console.log('✅ Production stock created successfully')

      return NextResponse.json({
        success: true,
        data: newStock.data,
        message: 'Production stock created successfully'
      })
    }

  } catch (error) {
    console.error('❌ Error adding production stock:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to add production stock',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// PATCH - Update production stock
export async function PATCH(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const body = await request.json()
    const { id, quantity, reserved_quantity, notes } = body

    console.log('🔄 Updating production stock:', { id, quantity, reserved_quantity })

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Stock ID is required' },
        { status: 400 }
      )
    }

    const updateData: any = {
      last_updated: new Date().toISOString()
    }

    if (quantity !== undefined) {
      updateData.quantity = quantity
      updateData.available_quantity = quantity - (reserved_quantity || 0)
    }

    if (reserved_quantity !== undefined) {
      updateData.reserved_quantity = reserved_quantity
      // Recalculate available quantity
      const currentStock = await fetch(`${DIRECTUS_URL}/items/production_stock/${id}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      })
      
      if (currentStock.ok) {
        const stockData = await currentStock.json()
        updateData.available_quantity = stockData.data.quantity - reserved_quantity
      }
    }

    const response = await fetch(`${DIRECTUS_URL}/items/production_stock/${id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(updateData)
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(`Failed to update stock: ${errorData.errors?.[0]?.message || 'Unknown error'}`)
    }

    const updatedStock = await response.json()

    console.log('✅ Production stock updated successfully')

    return NextResponse.json({
      success: true,
      data: updatedStock.data,
      message: 'Production stock updated successfully'
    })

  } catch (error) {
    console.error('❌ Error updating production stock:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update production stock',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
