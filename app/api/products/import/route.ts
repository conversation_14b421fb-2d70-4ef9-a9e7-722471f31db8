import { NextRequest, NextResponse } from 'next/server'
import directus, { createProduct, createConversionUnit, createStockMovementWithConversion, getVendors } from '@/lib/directus'

export const dynamic = 'force-dynamic'

interface ExcelRow {
  'No.': number
  '<PERSON><PERSON><PERSON> Barang': string
  'Kode Barang': string
  '<PERSON><PERSON>': string
  'Keterangan': string
  'Kuantitas': number
  'Satuan': string
  'Satuan #2': string
  'Rasio Satuan #2': number
  'Satuan #3': string
  'Rasio Satuan #3': number
  'Harga Jual': number
  'Harga Beli': number
  'Satuan Beli': string
  'ID Pemasok': string
  'Pemasok': string
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Starting product import process...')

    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      console.error('❌ No file uploaded')
      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 })
    }

    console.log('📁 File received:', file.name, 'Size:', file.size, 'Type:', file.type)

    // Read Excel file
    console.log('📖 Reading Excel file...')
    const buffer = await file.arrayBuffer()
    console.log('📦 Buffer created, size:', buffer.byteLength)

    // Dynamic import of XLSX
    const XLSX = await import('xlsx')

    const workbook = XLSX.read(buffer, { type: 'array' })
    console.log('📋 Workbook loaded, sheets:', workbook.SheetNames)

    if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
      throw new Error('No sheets found in Excel file')
    }

    const sheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[sheetName]

    if (!worksheet) {
      throw new Error(`Sheet "${sheetName}" not found`)
    }

    // Convert to JSON
    console.log('🔄 Converting sheet to JSON...')
    const jsonData: ExcelRow[] = XLSX.utils.sheet_to_json(worksheet)
    console.log('📊 Excel data parsed:', jsonData.length, 'rows')

    if (jsonData.length === 0) {
      throw new Error('No data found in Excel file')
    }

    // Get existing vendors for mapping
    const vendors = await getVendors()
    console.log('👥 Existing vendors:', vendors.length)

    const results = {
      success: 0,
      errors: [] as string[],
      products: [] as any[]
    }

    // Process each row
    for (let i = 0; i < jsonData.length; i++) {
      const row = jsonData[i]
      console.log(`\n🔄 Processing row ${i + 1}:`, row['Nama Barang'])

      try {
        // Validate required fields
        if (!row['Kode Barang'] || !row['Nama Barang'] || !row['Satuan']) {
          throw new Error(`Row ${i + 1}: Missing required fields (Kode Barang, Nama Barang, or Satuan)`)
        }

        // Find vendor by ID or name
        let vendorId = null
        if (row['ID Pemasok'] || row['Pemasok']) {
          const vendor = vendors.find(v =>
            v.vendor_code === row['ID Pemasok'] ||
            v.vendor_name === row['Pemasok']
          )
          vendorId = vendor?.id || null
        }

        // Create product
        const productData = {
          name: row['Nama Barang'],
          category: row['Kategori Barang'] || 'General',
          description: row['Keterangan'] || '',
          sku: row['Kode Barang'],
          unit: row['Satuan'], // Base unit
          purchase_price: row['Harga Beli'] || 0,
          min_stock: 0
        }

        console.log('📦 Creating product:', productData.name)
        const createdProduct = await createProduct(productData)
        const productId = (createdProduct as any).data?.id || (createdProduct as any).id

        if (!productId) {
          throw new Error(`Failed to create product: ${productData.name}`)
        }

        console.log('✅ Product created with ID:', productId)

        // Create conversion units if provided
        const conversions = []

        // Satuan #2
        if (row['Satuan #2'] && row['Rasio Satuan #2'] && row['Rasio Satuan #2'] > 0) {
          console.log('🔄 Creating conversion unit #2:', row['Satuan #2'], 'with ratio:', row['Rasio Satuan #2'])
          try {
            const conversion2 = await createConversionUnit({
              unit_name: row['Satuan #2'],
              conversion_to_base: row['Rasio Satuan #2'],
              id_produk_gudang: productId
            })
            conversions.push({ unit: row['Satuan #2'], data: conversion2 })
            console.log('✅ Conversion unit #2 created successfully')
          } catch (convError) {
            console.error('❌ Failed to create conversion unit #2:', convError)
          }
        }

        // Satuan #3
        if (row['Satuan #3'] && row['Rasio Satuan #3'] && row['Rasio Satuan #3'] > 0) {
          console.log('🔄 Creating conversion unit #3:', row['Satuan #3'], 'with ratio:', row['Rasio Satuan #3'])
          try {
            const conversion3 = await createConversionUnit({
              unit_name: row['Satuan #3'],
              conversion_to_base: row['Rasio Satuan #3'],
              id_produk_gudang: productId
            })
            conversions.push({ unit: row['Satuan #3'], data: conversion3 })
            console.log('✅ Conversion unit #3 created successfully')
          } catch (convError) {
            console.error('❌ Failed to create conversion unit #3:', convError)
          }
        }

        // Create initial stock movement if quantity > 0
        if (row['Kuantitas'] && row['Kuantitas'] > 0) {
          console.log('📈 Creating initial stock movement:', row['Kuantitas'], row['Satuan Beli'] || row['Satuan'])

          // Determine which unit to use for stock movement
          let unitUsed = 'base' // Default to base unit
          const stockUnit = row['Satuan Beli'] || row['Satuan']

          // Check if stock unit matches any conversion unit
          if (stockUnit !== row['Satuan']) {
            if (stockUnit === row['Satuan #2']) {
              // Find the conversion unit ID for Satuan #2
              const conv2 = conversions.find(c => c.unit === row['Satuan #2'])
              unitUsed = (conv2?.data as any)?.data?.id || (conv2?.data as any)?.id || 'base'
            } else if (stockUnit === row['Satuan #3']) {
              // Find the conversion unit ID for Satuan #3
              const conv3 = conversions.find(c => c.unit === row['Satuan #3'])
              unitUsed = (conv3?.data as any)?.data?.id || (conv3?.data as any)?.id || 'base'
            }
          }

          const stockMovement = {
            produk_gudang_id: productId,
            date: new Date().toISOString().split('T')[0], // Today's date
            type: 'incoming' as const,
            quantity: row['Kuantitas'],
            unit_used: unitUsed === 'base' ? undefined : unitUsed,
            vendor_id: vendorId || undefined,
            purchase_price: row['Harga Beli'] || 0,
            reference_number: `IMPORT-${new Date().getTime()}-${i + 1}`,
            notes: `Initial stock from Excel import - Row ${i + 1}`,
            created_by: 'system'
          }

          console.log('📊 Stock movement data:', stockMovement)
          await createStockMovementWithConversion(stockMovement)
          console.log('✅ Initial stock movement created')
        }

        results.success++
        results.products.push({
          row: i + 1,
          name: productData.name,
          sku: productData.sku,
          productId,
          conversions: conversions.length,
          initialStock: row['Kuantitas'] || 0
        })

        console.log(`✅ Row ${i + 1} processed successfully`)

      } catch (error) {
        const errorMessage = `Row ${i + 1} (${row['Nama Barang'] || 'Unknown'}): ${error instanceof Error ? error.message : 'Unknown error'}`
        console.error('❌', errorMessage)
        results.errors.push(errorMessage)
      }
    }

    console.log('\n🎉 Import process completed!')
    console.log('📊 Results:', {
      total: jsonData.length,
      success: results.success,
      errors: results.errors.length
    })

    return NextResponse.json({
      message: 'Import completed',
      total: jsonData.length,
      success: results.success,
      errors: results.errors.length,
      results
    })

  } catch (error) {
    console.error('❌ Import process failed:', error)
    return NextResponse.json(
      {
        error: 'Import failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
