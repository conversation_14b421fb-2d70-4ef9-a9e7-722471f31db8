import { NextResponse } from "next/server";
import { createSalesPlan } from "@/lib/directus";

export const runtime = "edge";

export async function POST(request: Request) {
  try {
    const data = await request.json();
    console.log("Received sales plan data:", data);

    const result = await createSalesPlan(data);
    console.log("Sales plan created:", result);

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error creating sales plan:", error);
    return NextResponse.json(
      { 
        message: error instanceof Error ? error.message : "Failed to create sales plan" 
      },
      { status: 500 }
    );
  }
} 