import { NextResponse } from "next/server";
import directus from "@/lib/directus";
import { readItems, createItem } from "@directus/sdk";
import type { ProductionStock } from "@/lib/directus";

export async function POST(request: Request) {
  try {
    // Get all products
    const products = await directus.request(
      readItems("produk", {
        filter: { status: { _eq: "published" } }
      })
    );
    
    console.log(`Found ${products.length} products to set up production stock`);
    
    // Check if production stock already exists for each product
    const results = [];
    for (const product of products) {
      const existingStock = await directus.request(
        readItems("production_stock", {
          filter: { produk: { _eq: String(product.id) } }
        })
      );
      
      if (existingStock.length === 0) {
        // Create initial stock with zero quantity
        const newStock = await directus.request(
          createItem("production_stock", {
            produk: String(product.id),
            quantity: 0,
            last_updated: new Date().toISOString()
          })
        );
        
        results.push({
          product: product.nama_produk,
          action: "created",
          id: newStock.id
        });
      } else {
        results.push({
          product: product.nama_produk,
          action: "exists",
          id: existingStock[0].id
        });
      }
    }
    
    return NextResponse.json({
      success: true,
      message: `Set up production stock for ${results.length} products`,
      results
    });
  } catch (error) {
    console.error("Error setting up production stock:", error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "Failed to set up production stock",
        error
      },
      { status: 500 }
    );
  }
} 