import { NextRequest, NextResponse } from 'next/server'

const DIRECTUS_URL = process.env.DIRECTUS_URL || 'http://localhost:8055'

export const dynamic = 'force-dynamic'

// Simple rate limiting - in production, use Redis or similar
const requestCounts = new Map<string, { count: number; resetTime: number }>()
const RATE_LIMIT = 100 // requests per window
const RATE_WINDOW = 60 * 1000 // 1 minute in milliseconds

function checkRateLimit(ip: string): boolean {
  const now = Date.now()
  const userRequests = requestCounts.get(ip)

  if (!userRequests || now > userRequests.resetTime) {
    requestCounts.set(ip, { count: 1, resetTime: now + RATE_WINDOW })
    return true
  }

  if (userRequests.count >= RATE_LIMIT) {
    return false
  }

  userRequests.count++
  return true
}

export async function GET(request: NextRequest) {
  try {
    // Rate limiting
    const ip = (request as any).ip || request.headers.get('x-forwarded-for') || 'unknown'
    if (!checkRateLimit(ip)) {
      console.log(`🚫 Rate limit exceeded for IP: ${ip}`)
      return NextResponse.json(
        {
          success: false,
          error: 'Rate limit exceeded',
          message: 'Too many requests. Please try again later.'
        },
        { status: 429 }
      )
    }

    // Validate authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log(`🔒 Unauthorized access attempt to bulk movement history from IP: ${ip}`)
      return NextResponse.json(
        {
          success: false,
          error: 'Unauthorized',
          message: 'Authentication required'
        },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    if (!token || token.length < 10) {
      console.log(`🔒 Invalid token format for bulk movement history from IP: ${ip}`)
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid token',
          message: 'Please login again'
        },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search') || ''
    const type = searchParams.get('type') || ''
    const dateFrom = searchParams.get('dateFrom') || ''
    const dateTo = searchParams.get('dateTo') || ''
    const vendor = searchParams.get('vendor') || ''

    console.log('🔍 Fetching bulk movement history with filters:', {
      page,
      limit,
      search,
      type,
      dateFrom,
      dateTo,
      vendor,
      timestamp: new Date().toISOString()
    })

    // Debug date parsing
    if (dateFrom || dateTo) {
      console.log('📅 Date filter debug:', {
        dateFrom,
        dateTo,
        dateFromParsed: dateFrom ? new Date(dateFrom).toISOString() : null,
        dateToParsed: dateTo ? new Date(dateTo).toISOString() : null,
        dateFromValid: dateFrom ? !isNaN(new Date(dateFrom).getTime()) : null,
        dateToValid: dateTo ? !isNaN(new Date(dateTo).getTime()) : null
      })
    }

    // Simplified approach - use fallback method directly to avoid complex query issues
    console.log('🔄 Using simplified fallback approach due to complex query limitations')

    let movements: any[] = []

    try {
      // Use simple filter to get bulk movements
      const simpleFilter = {
        _or: [
          { reference_number: { _starts_with: 'BULK-' } },
          { notes: { _contains: 'Bulk Movement:' } }
        ]
      }

      const fallbackParams = new URLSearchParams()
      fallbackParams.set('filter', JSON.stringify(simpleFilter))
      fallbackParams.set('sort', 'date_created')
      fallbackParams.set('limit', '1000')

      console.log('🔍 Simplified API Query:', `${DIRECTUS_URL}/items/StockMovement?${fallbackParams.toString()}`)

      const response = await fetch(`${DIRECTUS_URL}/items/StockMovement?${fallbackParams}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        }
      })

      if (response.ok) {
        const movementsData = await response.json()
        movements = movementsData.data || []
        console.log(`📊 Simplified API: Found ${movements.length} movements`)

        // Fetch product and vendor details for each movement
        for (const movement of movements) {
          if (movement.produk_gudang_id) {
            try {
              const productResponse = await fetch(`${DIRECTUS_URL}/items/ProdukGudang/${movement.produk_gudang_id}`, {
                headers: { 'Authorization': `Bearer ${token}` }
              })
              if (productResponse.ok) {
                const productData = await productResponse.json()
                movement.produk_gudang_id = productData.data
              }
            } catch (productError) {
              console.log('⚠️ Failed to fetch product details for:', movement.produk_gudang_id)
            }
          }

          if (movement.vendor_id) {
            try {
              const vendorResponse = await fetch(`${DIRECTUS_URL}/items/Vendor/${movement.vendor_id}`, {
                headers: { 'Authorization': `Bearer ${token}` }
              })
              if (vendorResponse.ok) {
                const vendorData = await vendorResponse.json()
                movement.vendor_id = vendorData.data
              }
            } catch (vendorError) {
              console.log('⚠️ Failed to fetch vendor details for:', movement.vendor_id)
            }
          }
        }
      } else {
        throw new Error(`API error: ${response.status}`)
      }
    } catch (error) {
      console.error('❌ Simplified API also failed:', error)
      throw new Error('Failed to fetch movements from API')
    }



    console.log(`📊 Found ${movements.length} movements`)

    // If no movements found, return empty result
    if (!movements || movements.length === 0) {
      console.log('📊 No movements found matching criteria')
      return NextResponse.json({
        success: true,
        data: [],
        pagination: {
          page,
          limit,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false
        }
      })
    }

    // Note: We'll handle pagination after grouping since we need to group by reference_number first

    // Group movements by reference_number and extract bulk movement info
    const bulkMovements = new Map()

    movements.forEach((movement: any) => {
      const ref = movement.reference_number || `SINGLE-${movement.id}`

      if (!bulkMovements.has(ref)) {
        // Extract bulk movement title from notes
        const notesMatch = movement.notes?.match(/Bulk Movement: ([^|]+)/)
        const bulkTitle = notesMatch ? notesMatch[1].trim() : 'Individual Movement'

        // Handle vendor data (could be object or ID depending on API method)
        let vendorData = null
        if (movement.vendor_id) {
          if (typeof movement.vendor_id === 'object') {
            vendorData = {
              id: movement.vendor_id.id,
              name: movement.vendor_id.vendor_name || 'Unknown Vendor',
              code: movement.vendor_id.vendor_code || ''
            }
          } else {
            vendorData = {
              id: movement.vendor_id,
              name: 'Vendor',
              code: ''
            }
          }
        }

        bulkMovements.set(ref, {
          reference_number: ref,
          title: bulkTitle,
          date: movement.date,
          type: movement.type,
          vendor: vendorData,
          items: [],
          total_items: 0,
          total_quantity: 0,
          total_cost: 0,
          created_at: movement.created_at,
          synced: true, // Will be updated if any item is not synced
          sync_errors: []
        })
      }

      const bulk = bulkMovements.get(ref)

      // Debug: Log product data structure
      console.log('🔍 Product data for movement:', movement.id, {
        produk_gudang_id: movement.produk_gudang_id,
        type: typeof movement.produk_gudang_id,
        isObject: typeof movement.produk_gudang_id === 'object',
        hasName: movement.produk_gudang_id?.name
      })

      // Handle product data (could be object or ID depending on API method)
      let productData = {
        id: movement.produk_gudang_id,
        name: 'Unknown Product',
        sku: '',
        category: '',
        unit: ''
      }

      if (movement.produk_gudang_id && typeof movement.produk_gudang_id === 'object') {
        productData = {
          id: movement.produk_gudang_id.id || movement.produk_gudang_id,
          name: movement.produk_gudang_id.name || 'Unknown Product',
          sku: movement.produk_gudang_id.sku || '',
          category: movement.produk_gudang_id.category || '',
          unit: movement.produk_gudang_id.unit || ''
        }
        console.log('✅ Product data extracted:', productData)
      } else {
        console.log('⚠️ Product data is not an object, ID only:', movement.produk_gudang_id)
      }

      bulk.items.push({
        id: movement.id,
        product: productData,
        quantity: movement.quantity,
        quantity_converted: movement.quantity_converted,
        unit_used: movement.unit_used,
        base_unit_label: movement.base_unit_label,
        purchase_price: movement.purchase_price || 0,
        total_cost: movement.total_cost || 0,
        notes: movement.notes,
        synced: movement.synced || false,
        accurate_id: movement.accurate_id,
        sync_error: movement.sync_error
      })

      bulk.total_items++
      bulk.total_quantity += movement.quantity || 0
      bulk.total_cost += movement.total_cost || 0

      // Update sync status
      if (!movement.synced) {
        bulk.synced = false
      }
      if (movement.sync_error) {
        bulk.sync_errors.push(movement.sync_error)
      }
    })

    // Convert map to array and apply filters
    let bulkMovementsArray = Array.from(bulkMovements.values())

    console.log(`📊 Before filtering: ${bulkMovementsArray.length} bulk movements`)

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase()
      bulkMovementsArray = bulkMovementsArray.filter(bulk =>
        bulk.title.toLowerCase().includes(searchLower) ||
        bulk.reference_number.toLowerCase().includes(searchLower) ||
        bulk.items.some((item: any) =>
          item.product.name.toLowerCase().includes(searchLower) ||
          item.product.sku.toLowerCase().includes(searchLower)
        )
      )
      console.log(`🔍 After search filter: ${bulkMovementsArray.length} bulk movements`)
    }

    // Apply filters on bulk movements (post-grouping filtering)
    console.log('🔍 Applying post-grouping filters:', { dateFrom, dateTo, type, vendor })

    // Apply date filter on bulk movements
    if (dateFrom || dateTo) {
      const beforeCount = bulkMovementsArray.length
      bulkMovementsArray = bulkMovementsArray.filter(bulk => {
        const bulkDate = new Date(bulk.date)
        let passesDateFilter = true

        if (dateFrom) {
          const fromDate = new Date(dateFrom)
          fromDate.setHours(0, 0, 0, 0) // Start of day
          passesDateFilter = passesDateFilter && bulkDate >= fromDate
        }

        if (dateTo) {
          const toDate = new Date(dateTo)
          toDate.setHours(23, 59, 59, 999) // End of day
          passesDateFilter = passesDateFilter && bulkDate <= toDate
        }

        // Debug individual date comparisons
        if (dateFrom || dateTo) {
          console.log('📅 Date comparison:', {
            bulkTitle: bulk.title,
            bulkDate: bulk.date,
            bulkDateParsed: bulkDate.toISOString(),
            dateFrom,
            dateTo,
            passesFilter: passesDateFilter
          })
        }

        return passesDateFilter
      })
      console.log(`📅 Date filter: ${beforeCount} → ${bulkMovementsArray.length} bulk movements`)
    }

    // Apply type filter on bulk movements
    if (type && type !== 'all') {
      const beforeCount = bulkMovementsArray.length
      bulkMovementsArray = bulkMovementsArray.filter(bulk => bulk.type === type)
      console.log(`🏷️ Type filter (${type}): ${beforeCount} → ${bulkMovementsArray.length} bulk movements`)
    }

    // Apply vendor filter on bulk movements
    if (vendor && vendor !== 'all') {
      const beforeCount = bulkMovementsArray.length
      bulkMovementsArray = bulkMovementsArray.filter(bulk => {
        const vendorMatch = bulk.vendor && bulk.vendor.id.toString() === vendor
        if (!vendorMatch && bulk.vendor) {
          console.log('🏢 Vendor mismatch:', {
            bulkVendorId: bulk.vendor.id,
            filterVendorId: vendor,
            bulkVendorName: bulk.vendor.name
          })
        }
        return vendorMatch
      })
      console.log(`🏢 Vendor filter (${vendor}): ${beforeCount} → ${bulkMovementsArray.length} bulk movements`)
    }

    // Sort bulk movements by date (newest first)
    bulkMovementsArray.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())

    // Calculate pagination for grouped results
    const totalBulkMovements = bulkMovementsArray.length
    const totalPages = Math.ceil(totalBulkMovements / limit)
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedBulkMovements = bulkMovementsArray.slice(startIndex, endIndex)

    console.log('📊 Final results:', {
      totalFound: totalBulkMovements,
      page,
      limit,
      totalPages,
      paginatedCount: paginatedBulkMovements.length,
      appliedFilters: {
        search: !!search,
        type: type !== 'all' ? type : null,
        vendor: vendor !== 'all' ? vendor : null,
        dateFrom,
        dateTo
      }
    })

    const response = NextResponse.json({
      success: true,
      data: paginatedBulkMovements,
      pagination: {
        page,
        limit,
        total: totalBulkMovements,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      },
      debug: {
        appliedFilters: {
          search: !!search,
          type: type !== 'all' ? type : null,
          vendor: vendor !== 'all' ? vendor : null,
          dateFrom,
          dateTo
        },
        totalMovementsBeforeGrouping: movements.length,
        totalBulkMovementsAfterGrouping: totalBulkMovements
      }
    })

    // Add security headers
    response.headers.set('X-Content-Type-Options', 'nosniff')
    response.headers.set('X-Frame-Options', 'DENY')
    response.headers.set('X-XSS-Protection', '1; mode=block')

    return response

  } catch (error) {
    console.error('❌ Error fetching bulk movement history:', error)

    // Check if it's a Directus API error
    if (error && typeof error === 'object' && 'errors' in error) {
      console.error('❌ Directus API Error:', JSON.stringify(error, null, 2))
      const errorObj = error as any
      return NextResponse.json(
        {
          success: false,
          error: 'Database query error',
          details: errorObj.errors?.[0]?.message || 'Invalid query parameters',
          directusError: error
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch bulk movement history',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
