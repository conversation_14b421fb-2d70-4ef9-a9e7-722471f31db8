import { NextRequest, NextResponse } from 'next/server'
import { createStockMovementWithConversion } from '@/lib/directus'

export const dynamic = 'force-dynamic'

interface BulkMovementItem {
  productId: string
  quantity: number
  unitUsed: string
  purchasePrice: number
  notes: string
}

interface BulkMovementRequest {
  type: 'incoming' | 'outgoing'
  date: string
  vendorId?: string
  referenceNumber?: string
  title: string
  generalNotes?: string
  items: BulkMovementItem[]
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Starting bulk stock movement process...')

    const body: BulkMovementRequest = await request.json()
    const { type, date, vendorId, referenceNumber, title, generalNotes, items } = body

    console.log('📋 Bulk movement request:', {
      type,
      date,
      vendorId,
      title,
      itemCount: items.length
    })

    // Validation
    if (!type || !date || !title || !items || items.length === 0) {
      return NextResponse.json(
        { error: 'Missing required fields: type, date, title, or items' },
        { status: 400 }
      )
    }

    if (type === 'incoming' && !vendorId) {
      return NextResponse.json(
        { error: 'Vendor is required for incoming stock movements' },
        { status: 400 }
      )
    }

    // Validate items
    const validItems = items.filter(item => item.productId && item.quantity > 0)
    if (validItems.length === 0) {
      return NextResponse.json(
        { error: 'No valid items found' },
        { status: 400 }
      )
    }

    console.log('✅ Validation passed, processing', validItems.length, 'items')

    const results = {
      success: 0,
      errors: [] as string[],
      movements: [] as any[]
    }

    // Generate shared reference number for all items in this bulk movement
    const sharedReference = referenceNumber || `BULK-${new Date().getTime()}`

    // Process each item
    for (let i = 0; i < validItems.length; i++) {
      const item = validItems[i]
      console.log(`\n🔄 Processing item ${i + 1}/${validItems.length}:`, item.productId)

      try {
        // Create individual stock movement with SHARED reference number
        const movementData = {
          produk_gudang_id: item.productId,
          date,
          type,
          quantity: item.quantity,
          unit_used: item.unitUsed === 'base' ? undefined : item.unitUsed,
          vendor_id: type === 'incoming' ? vendorId : undefined,
          purchase_price: item.purchasePrice || 0,
          reference_number: sharedReference, // SAME reference for all items
          notes: [
            `Bulk Movement: ${title}`,
            generalNotes && `General: ${generalNotes}`,
            item.notes && `Item: ${item.notes}`
          ].filter(Boolean).join(' | '),
          created_by: 'system',
          bulk_movement_title: title // Add bulk movement title for grouping
        }

        console.log('📊 Creating stock movement:', {
          productId: item.productId,
          quantity: item.quantity,
          type,
          reference: sharedReference
        })

        const createdMovement = await createStockMovementWithConversion(movementData, true) // Skip individual sync

        results.success++
        results.movements.push({
          item: i + 1,
          productId: item.productId,
          quantity: item.quantity,
          movementId: (createdMovement as any).data?.id || (createdMovement as any).id,
          reference: movementData.reference_number
        })

        console.log(`✅ Item ${i + 1} processed successfully`)

      } catch (error) {
        const errorMessage = `Item ${i + 1} (${item.productId}): ${error instanceof Error ? error.message : 'Unknown error'}`
        console.error('❌', errorMessage)
        results.errors.push(errorMessage)
      }
    }

    console.log('\n🎉 Bulk movement process completed!')
    console.log('📊 Results:', {
      total: validItems.length,
      success: results.success,
      errors: results.errors.length
    })

    // Trigger bulk sync to Accurate if all movements were successful
    let syncResult = null
    if (results.success > 0) {
      try {
        console.log(`🔄 Triggering bulk sync for reference: ${sharedReference}`)

        // Import accurateSync here to avoid circular dependency
        const { accurateSync } = await import('@/lib/accurate-sync')
        syncResult = await accurateSync.syncBulkMovements(sharedReference)

        if (syncResult.success) {
          console.log('✅ Bulk sync to Accurate completed successfully')
        } else {
          console.log('⚠️ Bulk sync to Accurate failed:', syncResult.error)
        }
      } catch (syncError) {
        console.error('❌ Error during bulk sync:', syncError)
        syncResult = {
          success: false,
          error: syncError instanceof Error ? syncError.message : 'Unknown sync error'
        }
      }
    }

    return NextResponse.json({
      message: 'Bulk stock movement completed',
      total: validItems.length,
      success: results.success,
      errors: results.errors.length,
      title,
      type,
      sharedReference,
      syncResult,
      results
    })

  } catch (error) {
    console.error('❌ Bulk movement process failed:', error)
    return NextResponse.json(
      {
        error: 'Bulk movement failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
