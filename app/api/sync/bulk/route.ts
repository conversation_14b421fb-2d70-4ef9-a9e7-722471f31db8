import { NextRequest, NextResponse } from 'next/server'
import { accurateSync } from '@/lib/accurate-sync'

export const dynamic = 'force-dynamic'

export async function POST(request: NextRequest) {
  try {
    const { referenceNumber } = await request.json()

    if (!referenceNumber) {
      return NextResponse.json(
        { error: 'Reference number is required' },
        { status: 400 }
      )
    }

    console.log(`🔄 Starting bulk sync for reference: ${referenceNumber}`)

    const result = await accurateSync.syncBulkMovements(referenceNumber)

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Bulk movements synced successfully',
        accurateId: result.accurateId,
        accurateNo: result.accurateNo
      })
    } else {
      return NextResponse.json({
        success: false,
        error: result.error
      }, { status: 400 })
    }

  } catch (error) {
    console.error('❌ Bulk sync API error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
