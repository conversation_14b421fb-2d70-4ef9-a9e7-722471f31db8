import { NextRequest, NextResponse } from 'next/server'
import { accurateSync } from '@/lib/accurate-sync'

export async function POST(request: NextRequest) {
  try {
    const { action, stockMovementId, limit } = await request.json()

    switch (action) {
      case 'sync_single':
        if (!stockMovementId) {
          return NextResponse.json({ error: 'stockMovementId required' }, { status: 400 })
        }
        
        const result = await accurateSync.syncStockMovement(stockMovementId)
        return NextResponse.json(result)

      case 'retry_failed':
        const retryLimit = limit || 10
        const results = await accurateSync.retryFailedSyncs(retryLimit)
        return NextResponse.json({ 
          success: true, 
          results,
          total: results.length,
          successful: results.filter(r => r.success).length,
          failed: results.filter(r => !r.success).length
        })

      case 'get_unsynced':
        const unsyncedLimit = limit || 50
        const unsynced = await accurateSync.getUnsyncedMovements(unsyncedLimit)
        return NextResponse.json({ 
          success: true, 
          data: unsynced,
          total: unsynced.length
        })

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }
  } catch (error) {
    console.error('Manual sync error:', error)
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')
    const limit = parseInt(searchParams.get('limit') || '50')

    switch (action) {
      case 'unsynced':
        const unsynced = await accurateSync.getUnsyncedMovements(limit)
        return NextResponse.json({ 
          success: true, 
          data: unsynced,
          total: unsynced.length
        })

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }
  } catch (error) {
    console.error('Manual sync GET error:', error)
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    }, { status: 500 })
  }
}
