import { NextRequest, NextResponse } from 'next/server'
import directus from '@/lib/directus'
import { readItems, updateItem } from '@directus/sdk'
import { format } from 'date-fns-tz'
import crypto from 'crypto'
import { callAccurateApi } from '../../accurate/accurateApi'

// Accurate API configuration
const ACCURATE_ACCESS_TOKEN = process.env.ACCURATE_ACCESS_TOKEN
const ACCURATE_SIGNATURE_SECRET = process.env.ACCURATE_SIGNATURE_SECRET

interface StockMovementPayload {
  event: 'items.create' | 'items.update'
  accountability: {
    user: string
    role: string
  }
  payload: {
    id: string
    produk_gudang_id: string
    type: 'incoming' | 'outgoing'
    quantity: number
    vendor_id?: string
    purchase_price?: number
    total_cost?: number
    date: string
    reference_number?: string
    notes?: string
    synced: boolean
    accurate_id?: string
    sync_error?: string
  }
}

interface AccurateResponse {
  s: boolean
  d?: {
    id: number
    no: string
  }
  e?: string
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Stock movement webhook received')

    // 1. Validasi payload
    const payload: StockMovementPayload = await request.json()

    if (!payload.payload || !payload.payload.id) {
      return NextResponse.json({ error: 'Invalid payload' }, { status: 400 })
    }

    const stockMovement = payload.payload
    console.log('📦 Processing stock movement:', stockMovement.id)

    // 2. Cek apakah sudah di-sync
    if (stockMovement.synced) {
      console.log('✅ Already synced, skipping')
      return NextResponse.json({ message: 'Already synced' })
    }

    // 3. Validasi data yang diperlukan untuk sync
    if (!stockMovement.vendor_id && stockMovement.type === 'incoming') {
      console.log('⚠️ No vendor for incoming movement, skipping sync')
      return NextResponse.json({ message: 'No vendor for incoming movement' })
    }

    // 4. Ambil data lengkap dari database
    const fullData = await getStockMovementWithRelations(stockMovement.id)
    if (!fullData) {
      throw new Error('Stock movement not found')
    }

    // 5. Bangun payload untuk Accurate
    let accuratePayload
    let accurateEndpoint

    if (stockMovement.type === 'incoming') {
      // Buat Purchase Invoice (PI)
      accuratePayload = await buildPurchaseInvoicePayload(fullData)
      accurateEndpoint = 'purchase-invoice/save.do'
    } else {
      // Buat Job Order (JO) untuk outgoing
      accuratePayload = await buildJobOrderPayload(fullData)
      accurateEndpoint = 'job-order.do'
    }

    console.log('🚀 Sending to Accurate:', accurateEndpoint)

    // 6. POST ke Accurate
    const accurateResponse = await sendToAccurate(accurateEndpoint, accuratePayload)

    if (accurateResponse.s) {
      // Success - mark as synced
      await updateStockMovementSync(stockMovement.id, {
        synced: true,
        accurate_id: accurateResponse.d?.id?.toString(),
        synced_at: new Date().toISOString(),
        sync_error: null
      })

      console.log('✅ Successfully synced to Accurate:', accurateResponse.d?.no)
      return NextResponse.json({
        success: true,
        accurate_id: accurateResponse.d?.id,
        accurate_no: accurateResponse.d?.no
      })
    } else {
      // Failed - save error
      await updateStockMovementSync(stockMovement.id, {
        synced: false,
        sync_error: accurateResponse.e || 'Unknown error from Accurate',
        synced_at: new Date().toISOString()
      })

      console.error('❌ Accurate sync failed:', accurateResponse.e)
      return NextResponse.json({
        success: false,
        error: accurateResponse.e
      }, { status: 500 })
    }

  } catch (error) {
    console.error('💥 Webhook error:', error)

    // Try to update sync error if we have the ID
    const payload = await request.json().catch(() => null)
    if (payload?.payload?.id) {
      await updateStockMovementSync(payload.payload.id, {
        synced: false,
        sync_error: error instanceof Error ? error.message : 'Unknown error',
        synced_at: new Date().toISOString()
      }).catch(console.error)
    }

    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Internal server error'
    }, { status: 500 })
  }
}

// Helper functions
async function getStockMovementWithRelations(id: string) {
  try {
    const movements = await directus.request(
      readItems('StockMovement', {
        filter: { id: { _eq: id } },
        fields: [
          '*',
          'produk_gudang_id.name',
          'produk_gudang_id.sku',
          'produk_gudang_id.unit',
          'vendor_id.vendor_name',
          'vendor_id.vendor_code',
          'vendor_id.tax_id'
        ]
      })
    )
    return movements[0] || null
  } catch (error) {
    console.error('Error fetching stock movement:', error)
    return null
  }
}

async function buildPurchaseInvoicePayload(stockMovement: any) {
  const vendor = stockMovement.vendor_id
  const product = stockMovement.produk_gudang_id

  return {
    transDate: stockMovement.date,
    vendorNo: vendor?.vendor_code || 'VENDOR001',
    vendorName: vendor?.vendor_name || 'Unknown Vendor',
    description: stockMovement.notes || `Stock incoming: ${product?.name}`,
    detailItem: [{
      itemNo: product?.sku || 'ITEM001',
      itemName: product?.name || 'Unknown Item',
      quantity: stockMovement.quantity_in_unit_used || stockMovement.quantity,
      unitPrice: stockMovement.purchase_price || 0,
      amount: stockMovement.total_cost || 0,
      unitName: stockMovement.unit_used || product?.unit || 'PCS'
    }],
    referenceNo: stockMovement.reference_number || '',
    taxableAmount: stockMovement.total_cost || 0,
    totalAmount: stockMovement.total_cost || 0
  }
}

async function buildJobOrderPayload(stockMovement: any) {
  const product = stockMovement.produk_gudang_id

  return {
    transDate: stockMovement.date,
    description: stockMovement.notes || `Stock outgoing: ${product?.name}`,
    detailItem: [{
      itemNo: product?.sku || 'ITEM001',
      itemName: product?.name || 'Unknown Item',
      quantity: stockMovement.quantity_in_unit_used || stockMovement.quantity,
      unitName: stockMovement.unit_used || product?.unit || 'PCS'
    }],
    referenceNo: stockMovement.reference_number || ''
  }
}

async function sendToAccurate(endpoint: string, payload: any): Promise<AccurateResponse> {
  try {
    if (!ACCURATE_ACCESS_TOKEN) {
      throw new Error('ACCURATE_ACCESS_TOKEN is not configured')
    }

    if (!ACCURATE_SIGNATURE_SECRET) {
      throw new Error('ACCURATE_SIGNATURE_SECRET is not configured')
    }

    const timestamp = format(new Date(), "yyyy-MM-dd'T'HH:mm:ss.SSSXXX", {
      timeZone: 'Asia/Jakarta',
    })

    const signature = crypto.createHmac('sha256', ACCURATE_SIGNATURE_SECRET).update(timestamp).digest('hex')

    const response = await callAccurateApi({
      endpoint,
      token: ACCURATE_ACCESS_TOKEN,
      signature,
      timestamp,
      data: payload,
    })

    // Transform response to match expected format
    if (response.data) {
      return {
        s: true,
        d: response.data
      }
    } else {
      return {
        s: false,
        e: 'No data returned from Accurate API'
      }
    }
  } catch (error: any) {
    console.error('Accurate API error:', error)
    return {
      s: false,
      e: error.response?.data?.message || error.message || 'Unknown error'
    }
  }
}

async function updateStockMovementSync(id: string, updates: {
  synced: boolean
  accurate_id?: string | null
  synced_at: string
  sync_error?: string | null
}) {
  try {
    await directus.request(
      updateItem('StockMovement', id, updates)
    )
  } catch (error) {
    console.error('Error updating sync status:', error)
    throw error
  }
}
