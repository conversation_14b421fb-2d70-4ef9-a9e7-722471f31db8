import { NextRequest, NextResponse } from 'next/server'
import { accurateSync } from '@/lib/accurate-sync'

export async function POST(request: NextRequest) {
  try {
    const { movementId } = await request.json()
    
    if (!movementId) {
      return NextResponse.json({ error: 'Movement ID required' }, { status: 400 })
    }

    console.log('🧪 Testing Accurate sync for movement:', movementId)
    
    // Trigger sync
    const result = await accurateSync.syncStockMovement(movementId)
    
    return NextResponse.json({
      success: true,
      result
    })
  } catch (error: any) {
    console.error('❌ Test sync error:', error)
    return NextResponse.json({
      error: error.message || 'Sync failed'
    }, { status: 500 })
  }
}
