"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export default function DirectusDebugPage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [collection, setCollection] = useState("")

  const runDebug = async (specificCollection?: string) => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      let url = '/api/debug-directus'
      if (specificCollection) {
        url += `?collection=${encodeURIComponent(specificCollection)}`
      }

      const response = await fetch(url)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to run debug')
      }

      setResult(data)
    } catch (err: any) {
      console.error('Debug error:', err)
      setError(err.message || String(err))
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container py-10">
      <h1 className="text-3xl font-bold mb-6">Directus Debug Tools</h1>
      
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Run Full Debug</CardTitle>
            <CardDescription>
              Check connection, authentication, and available collections
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              This will run a complete diagnostic on your Directus connection.
              Check the browser console and server logs for detailed output.
            </p>
          </CardContent>
          <CardFooter>
            <Button 
              onClick={() => runDebug()} 
              disabled={loading}
              className="w-full"
            >
              {loading ? "Running..." : "Run Full Debug"}
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Test Specific Collection</CardTitle>
            <CardDescription>
              Check if a specific collection is accessible
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="collection">Collection Name</Label>
              <Input
                id="collection"
                placeholder="e.g., ProdukGudang"
                value={collection}
                onChange={(e) => setCollection(e.target.value)}
              />
            </div>
          </CardContent>
          <CardFooter>
            <Button 
              onClick={() => runDebug(collection)}
              disabled={loading || !collection}
              className="w-full"
            >
              {loading ? "Testing..." : "Test Collection"}
            </Button>
          </CardFooter>
        </Card>
      </div>

      {error && (
        <div className="mt-6 p-4 bg-red-50 text-red-900 rounded-md">
          <h3 className="font-semibold">Error</h3>
          <p>{error}</p>
        </div>
      )}

      {result && (
        <div className="mt-6">
          <h2 className="text-xl font-semibold mb-2">Debug Results</h2>
          <div className="bg-slate-100 p-4 rounded-md overflow-auto max-h-96">
            <pre className="text-sm">{JSON.stringify(result, null, 2)}</pre>
          </div>
        </div>
      )}

      <div className="mt-8 text-sm text-muted-foreground">
        <h3 className="font-semibold mb-2">Debugging Tips</h3>
        <ul className="list-disc pl-5 space-y-1">
          <li>Check that your .env.local file has DIRECTUS_URL and DIRECTUS_TOKEN set</li>
          <li>Verify that your static token has the necessary permissions</li>
          <li>Make sure collection names match exactly what's in your Directus schema</li>
          <li>Look at server logs and browser console for detailed error messages</li>
        </ul>
      </div>
    </div>
  )
} 