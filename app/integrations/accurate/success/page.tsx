'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { CheckCircle, ArrowLeft, RefreshCw } from 'lucide-react'
import Link from 'next/link'

export default function AccurateSuccessPage() {
  const [tokenInfo, setTokenInfo] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Fetch token info to display success details
    fetchTokenInfo()
  }, [])

  const fetchTokenInfo = async () => {
    try {
      const response = await fetch('/api/accurate/status')
      if (response.ok) {
        const data = await response.json()
        setTokenInfo(data)
      }
    } catch (error) {
      console.error('Error fetching token info:', error)
    } finally {
      setLoading(false)
    }
  }

  const testConnection = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/accurate/test', { method: 'POST' })
      const result = await response.json()
      
      if (result.success) {
        alert('✅ Connection test successful!')
      } else {
        alert(`❌ Connection test failed: ${result.error}`)
      }
    } catch (error) {
      alert(`❌ Connection test failed: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-8">
      <Card className="max-w-2xl mx-auto">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <CheckCircle className="h-16 w-16 text-green-500" />
          </div>
          <CardTitle className="text-2xl text-green-600">
            Accurate Integration Successful!
          </CardTitle>
          <CardDescription>
            Your application has been successfully connected to Accurate Online
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {loading ? (
            <div className="text-center">
              <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
              <p>Loading integration details...</p>
            </div>
          ) : tokenInfo ? (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold mb-2">Integration Details:</h3>
              <div className="space-y-2 text-sm">
                <div><strong>Database:</strong> {tokenInfo.db_alias}</div>
                <div><strong>Host:</strong> {tokenInfo.host}</div>
                <div><strong>Scope:</strong> {tokenInfo.scope}</div>
                <div><strong>Expires:</strong> {new Date(tokenInfo.expires_at).toLocaleString()}</div>
              </div>
            </div>
          ) : (
            <div className="text-center text-gray-500">
              Integration completed successfully
            </div>
          )}

          <div className="space-y-3">
            <h3 className="font-semibold">What's Next?</h3>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• Stock movements will now automatically sync to Accurate</li>
              <li>• Purchase invoices will be created for incoming stock</li>
              <li>• Job orders will be created for outgoing stock</li>
              <li>• Monitor sync status in the dashboard</li>
            </ul>
          </div>

          <div className="flex gap-3">
            <Button onClick={testConnection} disabled={loading} className="flex-1">
              {loading ? (
                <RefreshCw className="h-4 w-4 animate-spin mr-2" />
              ) : null}
              Test Connection
            </Button>
            
            <Button asChild variant="outline" className="flex-1">
              <Link href="/sync">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Go to Sync Dashboard
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
