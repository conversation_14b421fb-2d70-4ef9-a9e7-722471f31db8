import { SalesPlanned } from '../types';

export async function getKangIders() {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/items/kangider?fields=*`,
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${process.env.NEXT_PUBLIC_API_TOKEN}`,
        },
      }
    );
    
    if (!response.ok) {
      throw new Error('Failed to fetch kang iders');
    }
    
    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error fetching kang iders:', error);
    throw error;
  }
}

export async function getSalesPlans(date?: string) {
  try {
    const dateFilter = date ? `&filter[date][_eq]=${date}` : '';
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/items/sales_planned?fields=*,kangider_id.*${dateFilter}`,
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${process.env.NEXT_PUBLIC_API_TOKEN}`,
        },
      }
    );
    
    if (!response.ok) {
      throw new Error('Failed to fetch sales plans');
    }
    
    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error fetching sales plans:', error);
    throw error;
  }
}

export async function createSalesPlan(salesPlan: Omit<SalesPlanned, 'id'>) {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/items/sales_planned`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${process.env.NEXT_PUBLIC_API_TOKEN}`,
        },
        body: JSON.stringify(salesPlan),
      }
    );
    
    if (!response.ok) {
      throw new Error('Failed to create sales plan');
    }
    
    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error creating sales plan:', error);
    throw error;
  }
}

export async function updateSalesPlan(id: string, salesPlan: Partial<SalesPlanned>) {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/items/sales_planned/${id}`,
      {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${process.env.NEXT_PUBLIC_API_TOKEN}`,
        },
        body: JSON.stringify(salesPlan),
      }
    );
    
    if (!response.ok) {
      throw new Error('Failed to update sales plan');
    }
    
    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error updating sales plan:', error);
    throw error;
  }
} 