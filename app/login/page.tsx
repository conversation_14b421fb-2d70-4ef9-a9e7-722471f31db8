'use client'

import { Suspense } from 'react'
import { Package } from 'lucide-react'
import { LoginForm } from '.'

export default function LoginPage() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-background">
      <div className="mb-8 flex flex-col items-center">
        <Package className="h-12 w-12 text-primary mb-2" />
        <h1 className="text-2xl font-bold">Gudang App</h1>
      </div>

      <Suspense fallback={<div>Loading form...</div>}>
        <LoginForm />
      </Suspense>
    </div>
  )
} 