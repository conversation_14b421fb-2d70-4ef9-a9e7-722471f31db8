"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Package, ArrowDownCircle, ArrowUpCircle, AlertTriangle, Loader2, Coffee, TrendingUp, Calendar, RefreshCw } from "lucide-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { getProducts, getStockMovements } from "@/lib/directus"
import type { ProdukGudang, StockMovement } from "@/lib/directus"
import { ProtectedRoute } from '@/components/auth/protected-route'
import { useAuth } from '@/lib/auth'

export default function Dashboard() {
  const [isLoading, setIsLoading] = useState(true)
  const [products, setProducts] = useState<ProdukGudang[]>([])
  const [movements, setMovements] = useState<StockMovement[]>([])
  const { user } = useAuth()
  const [stats, setStats] = useState({
    totalProducts: 0,
    incomingStock: 0,
    outgoingStock: 0,
    lowStockProducts: [] as ProdukGudang[],
    currentStocks: {} as Record<string, number>
  })

  // Mock production data - in a real app, this would come from an API
  const productionStats = {
    totalProduction: 245,
    todayProduction: 32,
    activeProducts: 5,
    topProduct: "Arabica Medium Roast"
  }

  useEffect(() => {
    async function fetchData() {
      try {
        // Fetch products
        const productsData = await getProducts()
        setProducts(productsData)

        // Fetch all stock movements
        const movementsData = await getStockMovements()
        setMovements(movementsData)

        // Calculate stats
        const thirtyDaysAgo = new Date()
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
        const recentDate = thirtyDaysAgo.toISOString()

        // Calculate product stocks based on movements
        const productStocks: Record<string, number> = {}

        // Initialize all products with 0 stock
        productsData.forEach(product => {
          if (product.id) {
            productStocks[product.id] = 0
          }
        })

        // Calculate current stock for each product
        movementsData.forEach(movement => {
          if (movement.produk_gudang_id) {
            if (movement.type === "incoming") {
              productStocks[movement.produk_gudang_id] = (productStocks[movement.produk_gudang_id] || 0) + movement.quantity
            } else {
              productStocks[movement.produk_gudang_id] = (productStocks[movement.produk_gudang_id] || 0) - movement.quantity
            }
          }
        })

        // Calculate last 30 days movement totals
        const recentMovements = movementsData.filter(
          m => new Date(m.date) >= thirtyDaysAgo
        )

        const incomingTotal = recentMovements
          .filter(m => m.type === "incoming")
          .reduce((sum, m) => sum + m.quantity, 0)

        const outgoingTotal = recentMovements
          .filter(m => m.type === "outgoing")
          .reduce((sum, m) => sum + m.quantity, 0)

        // Find low stock products
        const lowStock = productsData.filter(p => {
          if (!p.id) return false
          const currentStock = productStocks[p.id] || 0
          return currentStock < p.min_stock
        })

        // Set all calculated stats
        setStats({
          totalProducts: productsData.length,
          incomingStock: incomingTotal,
          outgoingStock: outgoingTotal,
          lowStockProducts: lowStock,
          currentStocks: productStocks
        })
      } catch (error) {
        console.error("Error fetching data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  // Show a loading state while data is being fetched
  if (isLoading) {
    return (
      <ProtectedRoute>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <h2 className="text-lg font-semibold">Loading dashboard data</h2>
            <p className="text-muted-foreground">Please wait while we load your warehouse data</p>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
            <p className="text-muted-foreground">
              Welcome, {user?.first_name}! You are logged in as {user?.email}
            </p>
          </div>
          <div className="flex gap-2">
            <Button asChild>
              <Link href="/products/add">
                <Package className="mr-2 h-4 w-4" />
                Add Product
              </Link>
            </Button>
            <Button asChild>
              <Link href="/stock/add-movement">
                <ArrowDownCircle className="mr-2 h-4 w-4" />
                Record Movement
              </Link>
            </Button>
          </div>
        </div>

        {/* Dashboard Links */}
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader className="bg-muted/50">
              <CardTitle className="flex items-center">
                <Coffee className="mr-2 h-5 w-5" />
                Production Dashboard
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-4">
              <p className="text-sm text-muted-foreground mb-4">
                Monitor production metrics, daily output, and product performance
              </p>
              <Button className="w-full" asChild>
                <Link href="/production/dashboard">Go to Production Dashboard</Link>
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="bg-muted/50">
              <CardTitle className="flex items-center">
                <RefreshCw className="mr-2 h-5 w-5" />
                Accurate Sync Dashboard
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-4">
              <p className="text-sm text-muted-foreground mb-4">
                Monitor and manage stock movement synchronization with Accurate ERP
              </p>
              <Button className="w-full" asChild>
                <Link href="/sync">Go to Sync Dashboard</Link>
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Inventory Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Products</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalProducts}</div>
              <p className="text-xs text-muted-foreground">Products in your inventory</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Incoming Stock</CardTitle>
              <ArrowDownCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">+{stats.incomingStock}</div>
              <p className="text-xs text-muted-foreground">Last 30 days</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Outgoing Stock</CardTitle>
              <ArrowUpCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">-{stats.outgoingStock}</div>
              <p className="text-xs text-muted-foreground">Last 30 days</p>
            </CardContent>
          </Card>
        </div>

        {/* Production Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Production</CardTitle>
              <Coffee className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{productionStats.totalProduction}</div>
              <p className="text-xs text-muted-foreground">Units produced in the last 7 days</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Today's Production</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{productionStats.todayProduction}</div>
              <p className="text-xs text-muted-foreground">Units produced today</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Products</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{productionStats.activeProducts}</div>
              <p className="text-xs text-muted-foreground">Products in production</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Top Product</CardTitle>
              <Coffee className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold truncate">{productionStats.topProduct}</div>
              <p className="text-xs text-muted-foreground">Most produced product</p>
            </CardContent>
          </Card>
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Low Stock Alert</CardTitle>
              <CardDescription>Products that are below minimum stock level</CardDescription>
            </CardHeader>
            <CardContent>
              {stats.lowStockProducts.length === 0 ? (
                <p className="text-muted-foreground">No products are below minimum stock level.</p>
              ) : (
                <div className="space-y-4">
                  {stats.lowStockProducts.slice(0, 3).map((product) => (
                    <div key={product.id} className="flex items-center gap-4">
                      <AlertTriangle className="h-8 w-8 text-amber-500" />
                      <div className="flex-1">
                        <p className="font-medium">{product.name}</p>
                        <p className="text-sm text-muted-foreground">
                          Current: {stats.currentStocks[product.id || '']} {product.unit}, Minimum: {product.min_stock} {product.unit}
                        </p>
                      </div>
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/stock/add-movement?product=${product.id}&type=incoming`}>Restock</Link>
                      </Button>
                    </div>
                  ))}
                  {stats.lowStockProducts.length > 3 && (
                    <div className="text-center mt-2">
                      <Button variant="link" asChild>
                        <Link href="/products">View all {stats.lowStockProducts.length} low stock products</Link>
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Recent Movements</CardTitle>
              <CardDescription>Latest stock movements</CardDescription>
            </CardHeader>
            <CardContent>
              {movements.length === 0 ? (
                <p className="text-muted-foreground">No recent stock movements.</p>
              ) : (
                <div className="space-y-4">
                  {movements.slice(0, 4).map((movement) => {
                    const product = products.find(p => p.id === movement.produk_gudang_id)
                    return (
                      <div key={movement.id} className="flex items-center gap-4">
                        {movement.type === "outgoing" ? (
                          <ArrowUpCircle className="h-8 w-8 text-red-500" />
                        ) : (
                          <ArrowDownCircle className="h-8 w-8 text-green-500" />
                        )}
                        <div className="flex-1">
                          <p className="font-medium">{product?.name || 'Unknown Product'}</p>
                          <p className="text-sm text-muted-foreground">
                            {movement.type === "outgoing" ? "-" : "+"}
                            {movement.quantity} {product?.unit || 'units'} • {new Date(movement.date).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    )
                  })}
                  {movements.length > 4 && (
                    <div className="text-center mt-2">
                      <Button variant="link" asChild>
                        <Link href="/stock">View all stock movements</Link>
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </ProtectedRoute>
  )
}

