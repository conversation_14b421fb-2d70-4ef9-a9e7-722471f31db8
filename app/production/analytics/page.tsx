"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import { 
  ArrowLeft, 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Calendar,
  Package,
  Truck,
  RotateCcw,
  Target,
  AlertCircle
} from "lucide-react"
import { format, subDays, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from "date-fns"

interface AnalyticsData {
  period: string
  production: {
    total: number
    target: number
    variance: number
    products: {
      name: string
      produced: number
      target: number
      variance: number
    }[]
  }
  distribution: {
    total: number
    channels: {
      name: string
      type: string
      distributed: number
      percentage: number
    }[]
  }
  returns: {
    total: number
    rate: number
    reasons: {
      reason: string
      count: number
      percentage: number
    }[]
  }
  efficiency: {
    productionEfficiency: number
    distributionRate: number
    returnRate: number
    overallScore: number
  }
}

export default function ProductionAnalyticsPage() {
  const router = useRouter()
  const { toast } = useToast()
  
  const [isLoading, setIsLoading] = useState(true)
  const [period, setPeriod] = useState("today")
  const [startDate, setStartDate] = useState(format(new Date(), "yyyy-MM-dd"))
  const [endDate, setEndDate] = useState(format(new Date(), "yyyy-MM-dd"))
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null)

  useEffect(() => {
    // Update date range based on period selection
    const today = new Date()
    switch (period) {
      case "today":
        setStartDate(format(today, "yyyy-MM-dd"))
        setEndDate(format(today, "yyyy-MM-dd"))
        break
      case "yesterday":
        const yesterday = subDays(today, 1)
        setStartDate(format(yesterday, "yyyy-MM-dd"))
        setEndDate(format(yesterday, "yyyy-MM-dd"))
        break
      case "week":
        setStartDate(format(startOfWeek(today), "yyyy-MM-dd"))
        setEndDate(format(endOfWeek(today), "yyyy-MM-dd"))
        break
      case "month":
        setStartDate(format(startOfMonth(today), "yyyy-MM-dd"))
        setEndDate(format(endOfMonth(today), "yyyy-MM-dd"))
        break
    }
  }, [period])

  useEffect(() => {
    async function fetchAnalytics() {
      setIsLoading(true)
      try {
        // Mock data for now - replace with actual API call
        const mockData: AnalyticsData = {
          period: `${startDate} to ${endDate}`,
          production: {
            total: 150,
            target: 200,
            variance: -25,
            products: [
              { name: "Kopi Susu", produced: 80, target: 100, variance: -20 },
              { name: "Kopi Hitam", produced: 70, target: 100, variance: -30 }
            ]
          },
          distribution: {
            total: 120,
            channels: [
              { name: "Kang Ider A", type: "kangider", distributed: 50, percentage: 41.7 },
              { name: "SD Negeri 1", type: "sekolah", distributed: 40, percentage: 33.3 },
              { name: "Reseller B", type: "reseller", distributed: 30, percentage: 25.0 }
            ]
          },
          returns: {
            total: 15,
            rate: 12.5,
            reasons: [
              { reason: "Tidak laku", count: 8, percentage: 53.3 },
              { reason: "Rusak", count: 4, percentage: 26.7 },
              { reason: "Kadaluarsa", count: 3, percentage: 20.0 }
            ]
          },
          efficiency: {
            productionEfficiency: 75,
            distributionRate: 80,
            returnRate: 12.5,
            overallScore: 77.5
          }
        }

        setAnalyticsData(mockData)
      } catch (error) {
        console.error("Error fetching analytics:", error)
        toast({
          title: "Error",
          description: "Failed to load analytics data",
          variant: "destructive"
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchAnalytics()
  }, [startDate, endDate, toast])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-lg font-semibold">Loading analytics...</h2>
          <p className="text-muted-foreground">Please wait</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center space-x-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Production Analytics</h1>
          <p className="text-muted-foreground">
            Comprehensive insights into production performance
          </p>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Time Period</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label>Quick Select</Label>
              <Select value={period} onValueChange={setPeriod}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="yesterday">Yesterday</SelectItem>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                  <SelectItem value="custom">Custom Range</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label>Start Date</Label>
              <Input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                disabled={period !== "custom"}
              />
            </div>
            
            <div className="space-y-2">
              <Label>End Date</Label>
              <Input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                disabled={period !== "custom"}
              />
            </div>

            <div className="flex items-end">
              <Button className="w-full">
                <BarChart3 className="h-4 w-4 mr-2" />
                Update
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      {analyticsData && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Production</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analyticsData.production.total}</div>
                <div className="flex items-center text-xs text-muted-foreground">
                  <Target className="h-3 w-3 mr-1" />
                  Target: {analyticsData.production.target}
                  {analyticsData.production.variance !== 0 && (
                    <span className={`ml-2 flex items-center ${
                      analyticsData.production.variance > 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {analyticsData.production.variance > 0 ? (
                        <TrendingUp className="h-3 w-3 mr-1" />
                      ) : (
                        <TrendingDown className="h-3 w-3 mr-1" />
                      )}
                      {Math.abs(analyticsData.production.variance)}%
                    </span>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Distributed</CardTitle>
                <Truck className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analyticsData.distribution.total}</div>
                <p className="text-xs text-muted-foreground">
                  {((analyticsData.distribution.total / analyticsData.production.total) * 100).toFixed(1)}% of production
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Returns</CardTitle>
                <RotateCcw className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analyticsData.returns.total}</div>
                <p className="text-xs text-muted-foreground">
                  {analyticsData.returns.rate}% return rate
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Efficiency Score</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analyticsData.efficiency.overallScore}%</div>
                <p className="text-xs text-muted-foreground">
                  Overall performance
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Detailed Analytics */}
          <Tabs defaultValue="production" className="space-y-4">
            <TabsList>
              <TabsTrigger value="production">Production</TabsTrigger>
              <TabsTrigger value="distribution">Distribution</TabsTrigger>
              <TabsTrigger value="returns">Returns</TabsTrigger>
              <TabsTrigger value="efficiency">Efficiency</TabsTrigger>
            </TabsList>

            <TabsContent value="production" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Production Performance</CardTitle>
                  <CardDescription>Product-wise production vs targets</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analyticsData.production.products.map((product, index) => (
                      <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <p className="font-medium">{product.name}</p>
                          <p className="text-sm text-muted-foreground">
                            Target: {product.target} units
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-bold">{product.produced}</p>
                          <Badge variant={product.variance >= 0 ? "default" : "destructive"}>
                            {product.variance >= 0 ? "+" : ""}{product.variance}%
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="distribution" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Distribution Channels</CardTitle>
                  <CardDescription>Distribution breakdown by channel</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analyticsData.distribution.channels.map((channel, index) => (
                      <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <p className="font-medium">{channel.name}</p>
                          <Badge variant="outline">{channel.type}</Badge>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-bold">{channel.distributed}</p>
                          <p className="text-sm text-muted-foreground">
                            {channel.percentage.toFixed(1)}%
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="returns" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Return Analysis</CardTitle>
                  <CardDescription>Return reasons and patterns</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analyticsData.returns.reasons.map((reason, index) => (
                      <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <p className="font-medium">{reason.reason}</p>
                          <p className="text-sm text-muted-foreground">
                            {reason.percentage.toFixed(1)}% of returns
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-bold">{reason.count}</p>
                          <p className="text-sm text-muted-foreground">items</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="efficiency" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Efficiency Metrics</CardTitle>
                  <CardDescription>Key performance indicators</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Production Efficiency</span>
                        <span className="text-lg font-bold">{analyticsData.efficiency.productionEfficiency}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-500 h-2 rounded-full" 
                          style={{ width: `${analyticsData.efficiency.productionEfficiency}%` }}
                        ></div>
                      </div>
                    </div>

                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Distribution Rate</span>
                        <span className="text-lg font-bold">{analyticsData.efficiency.distributionRate}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-green-500 h-2 rounded-full" 
                          style={{ width: `${analyticsData.efficiency.distributionRate}%` }}
                        ></div>
                      </div>
                    </div>

                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Return Rate</span>
                        <span className="text-lg font-bold">{analyticsData.efficiency.returnRate}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-red-500 h-2 rounded-full" 
                          style={{ width: `${analyticsData.efficiency.returnRate}%` }}
                        ></div>
                      </div>
                    </div>

                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Overall Score</span>
                        <span className="text-lg font-bold">{analyticsData.efficiency.overallScore}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-purple-500 h-2 rounded-full" 
                          style={{ width: `${analyticsData.efficiency.overallScore}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  )
}
