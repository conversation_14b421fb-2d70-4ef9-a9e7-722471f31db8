"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Coffee, TrendingUp, Target, Calendar } from "lucide-react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { format } from "date-fns"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { getProduk, getDailyProduction, getProductionSummary } from "@/lib/directus"
import { useToast } from "@/components/ui/use-toast"
import { Produk } from "@/lib/directus"

export default function ProductionDashboardPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [productionByProduct, setProductionByProduct] = useState<any[]>([])
  const [dailyTotals, setDailyTotals] = useState<any[]>([])
  const [totalProduction, setTotalProduction] = useState(0)
  const [products, setProducts] = useState<Produk[]>([])
  const { toast } = useToast()

  useEffect(() => {
    async function fetchData() {
      try {
        const today = new Date()
        const sevenDaysAgo = new Date(today)
        sevenDaysAgo.setDate(today.getDate() - 7)

        const [productsData, productionData, summaryData] = await Promise.all([
          getProduk(),
          getDailyProduction({
            date: {
              _gte: sevenDaysAgo.toISOString().split('T')[0],
              _lte: today.toISOString().split('T')[0]
            } as any
          }),
          getProductionSummary(
            sevenDaysAgo.toISOString().split('T')[0],
            today.toISOString().split('T')[0]
          )
        ])

        setProducts(productsData)
        setProductionByProduct(summaryData)

        // Calculate daily totals
        const totals = productionData.reduce((acc: any, prod: any) => {
          const date = prod.date
          if (!acc[date]) {
            acc[date] = {
              date,
              total: 0,
              productions: []
            }
          }
          acc[date].total += prod.quantity_produced
          acc[date].productions.push(prod)
          return acc
        }, {})

        setDailyTotals(Object.values(totals))
        setTotalProduction(Object.values(totals).reduce((sum: number, day: any) => sum + day.total, 0))
      } catch (error) {
        console.error("Error fetching dashboard data:", error)
        toast({
          title: "Error",
          description: "Failed to load dashboard data",
          variant: "destructive"
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [toast])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-lg font-semibold">Loading dashboard data...</h2>
          <p className="text-muted-foreground">Please wait</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Production Dashboard</h1>
          <p className="text-muted-foreground">Overview of your coffee production metrics</p>
        </div>
        <div className="flex gap-2">
          <Button asChild>
            <Link href="/production/add">
              <Coffee className="mr-2 h-4 w-4" />
              Record Production
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/production">
              <Calendar className="mr-2 h-4 w-4" />
              View All
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Production</CardTitle>
            <Coffee className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalProduction}</div>
            <p className="text-xs text-muted-foreground">Units produced in the last 7 days</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Products</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{products.length}</div>
            <p className="text-xs text-muted-foreground">Total coffee products</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Top Product</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {productionByProduct?.length > 0 ? (
              <>
                <div className="text-2xl font-bold">
                  {productionByProduct
                    .sort((a, b) => b.totalProduced - a.totalProduced)[0]
                    ?.product?.nama_produk || "No product data"}
                </div>
                <p className="text-xs text-muted-foreground">Most produced in the last 7 days</p>
              </>
            ) : (
              <div className="text-muted-foreground">No data available</div>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Production</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {dailyTotals.length > 0 ? (
              <>
                <div className="text-2xl font-bold">{dailyTotals[dailyTotals.length - 1].total}</div>
                <p className="text-xs text-muted-foreground">Units produced today</p>
              </>
            ) : (
              <div className="text-muted-foreground">No data available</div>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Daily Production</CardTitle>
            <CardDescription>Production quantities for the last 7 days</CardDescription>
          </CardHeader>
          <CardContent>
            {dailyTotals.length === 0 ? (
              <div className="text-center py-6">
                <p className="text-muted-foreground">No production data available for the last 7 days.</p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Total Production</TableHead>
                    <TableHead>Products</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {dailyTotals.map((day) => (
                    <TableRow key={day.date}>
                      <TableCell>{format(new Date(day.date), "dd MMM yyyy")}</TableCell>
                      <TableCell>{day.total}</TableCell>
                      <TableCell>{day.productions.length}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Production by Product</CardTitle>
            <CardDescription>Total production for each product in the last 7 days</CardDescription>
          </CardHeader>
          <CardContent>
            {productionByProduct.length === 0 ? (
              <div className="text-center py-6">
                <p className="text-muted-foreground">No production data available.</p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Product</TableHead>
                    <TableHead>Total Produced</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {productionByProduct
                    .sort((a, b) => b.totalProduced - a.totalProduced)
                    .map((item) => (
                      <TableRow key={item.product.id}>
                        <TableCell className="font-medium">{item.product.nama_produk}</TableCell>
                        <TableCell>{item.totalProduced}</TableCell>
                        <TableCell>
                          {item.totalProduced > 0 ? (
                            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                              Active
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                              No Production
                            </Badge>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

