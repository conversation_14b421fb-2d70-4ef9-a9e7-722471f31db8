"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "@/hooks/use-toast"
import { AlertTriangle } from "lucide-react"
import { removeDailyProduction } from "@/app/actions"

interface DeleteProductionFormProps {
  production: any
}

export function DeleteProductionForm({ production }: DeleteProductionFormProps) {
  const router = useRouter()
  const [isDeleting, setIsDeleting] = useState(false)

  async function handleDelete() {
    setIsDeleting(true)

    try {
      const result = await removeDailyProduction(production.id)

      if (result.success) {
        toast({
          title: "Production record deleted",
          description: "The production record has been deleted successfully.",
        })

        router.push("/production")
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete production record. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Delete Production Record</h1>
        <p className="text-muted-foreground">Confirm deletion of production record</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            Confirm Deletion
          </CardTitle>
          <CardDescription>
            Are you sure you want to delete this production record? This action cannot be undone.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Product</p>
                <p>{production.produk?.name || `Product ${production.produk_id}`}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Date</p>
                <p>{new Date(production.date).toLocaleDateString()}</p>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Quantity Produced</p>
                <p>{production.quantity_produced}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Target Quantity</p>
                <p>{production.target_quantity || "-"}</p>
              </div>
            </div>
            {production.notes && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Notes</p>
                <p>{production.notes}</p>
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" type="button" onClick={() => router.back()}>
            Cancel
          </Button>
          <Button variant="destructive" onClick={handleDelete} disabled={isDeleting}>
            {isDeleting ? "Deleting..." : "Delete Production Record"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}

