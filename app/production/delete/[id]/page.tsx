import { fetchDailyProduction } from "@/app/actions"
import { DeleteProductionForm } from "../../delete-production-form"
import { notFound } from "next/navigation"

export default async function DeleteProductionPage({
  params,
}: {
  params: { id: string }
}) {
  const { success, data: productions = [] } = await fetchDailyProduction({
    id: { _eq: params.id },
  })

  if (!success || productions.length === 0) {
    return notFound()
  }

  const productionData = productions[0]

  return <DeleteProductionForm production={productionData} />
}

