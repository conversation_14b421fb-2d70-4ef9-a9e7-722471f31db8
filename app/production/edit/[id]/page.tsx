import { fetchCoffeeProducts, fetchDailyProduction } from "@/app/actions"
import { ProductionForm } from "../../production-form"
import { notFound } from "next/navigation"

export default async function EditProductionPage({
  params,
}: {
  params: { id: string }
}) {
  const { success: productsSuccess, data: coffeeProducts = [] } = await fetchCoffeeProducts()
  const { success: productionSuccess, data: productions = [] } = await fetchDailyProduction({
    id: { _eq: params.id },
  })

  if (!productionSuccess || productions.length === 0) {
    return notFound()
  }

  const productionData = productions[0]

  return (
    <ProductionForm
      products={coffeeProducts}
      initialProductId={productionData.produk_id}
      mode="edit"
      productionData={productionData}
    />
  )
}

