"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import { 
  ArrowLeft, 
  Eye, 
  Calendar, 
  DollarSign, 
  Package, 
  TrendingUp,
  TrendingDown,
  Search,
  Filter
} from "lucide-react"
import { format } from "date-fns"
import { 
  getKasHarian, 
  getStockHarian,
  getKangIders, 
  getProduk,
  type KasHarian,
  type StockHarian,
  type KangIder,
  type Produk
} from "@/lib/directus"

export default function KasHarianPage() {
  const router = useRouter()
  const { toast } = useToast()
  
  const [isLoading, setIsLoading] = useState(true)
  const [kasHarian, setKasHarian] = useState<KasHarian[]>([])
  const [stockHarian, setStockHarian] = useState<StockHarian[]>([])
  const [kangIders, setKangIders] = useState<KangIder[]>([])
  const [products, setProducts] = useState<Produk[]>([])
  const [filteredKas, setFilteredKas] = useState<KasHarian[]>([])
  
  // Filters
  const [searchTerm, setSearchTerm] = useState("")
  const [kangIderFilter, setKangIderFilter] = useState<string>("all")
  const [dateFilter, setDateFilter] = useState<string>("")

  useEffect(() => {
    async function fetchData() {
      try {
        const [kasHarianData, stockHarianData, kangIdersData, productsData] = await Promise.all([
          getKasHarian(),
          getStockHarian(),
          getKangIders(),
          getProduk()
        ])

        setKasHarian(kasHarianData)
        setStockHarian(stockHarianData)
        setKangIders(kangIdersData)
        setProducts(productsData)
        setFilteredKas(kasHarianData)
      } catch (error) {
        console.error("Error fetching data:", error)
        toast({
          title: "Error",
          description: "Failed to load data",
          variant: "destructive"
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [toast])

  // Apply filters
  useEffect(() => {
    let filtered = kasHarian

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(kas => {
        const kangIder = kangIders.find(k => k.id === kas.idkangider)
        return kangIder?.nama.toLowerCase().includes(searchTerm.toLowerCase()) ||
               kas.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
               kas.keterangan?.toLowerCase().includes(searchTerm.toLowerCase())
      })
    }

    // KangIder filter
    if (kangIderFilter !== "all") {
      filtered = filtered.filter(kas => kas.idkangider === kangIderFilter)
    }

    // Date filter
    if (dateFilter) {
      filtered = filtered.filter(kas => kas.tanggal === dateFilter)
    }

    setFilteredKas(filtered)
  }, [kasHarian, searchTerm, kangIderFilter, dateFilter, kangIders])

  const getKangIderName = (kangIderId: string) => {
    const kangIder = kangIders.find(k => k.id === kangIderId)
    return kangIder?.nama || "Unknown"
  }

  const getStockHarianForKas = (kasId: string) => {
    return stockHarian.filter(stock => stock.kas_harian === kasId)
  }

  const calculateStockVariance = (stockAwal: number, stockAkhir: number) => {
    const variance = stockAkhir - stockAwal
    const percentage = stockAwal > 0 ? (variance / stockAwal) * 100 : 0
    return { variance, percentage }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-lg font-semibold">Loading...</h2>
          <p className="text-muted-foreground">Please wait</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center space-x-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Kas Harian Kang Ider</h1>
          <p className="text-muted-foreground">
            Daily cash and stock management for Kang Ider
          </p>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Kas Harian</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredKas.length}</div>
            <p className="text-xs text-muted-foreground">
              Active records
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Penjualan</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {filteredKas.reduce((sum, kas) => sum + (kas.total_penjualan || 0), 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Total sales amount
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Stock Awal</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {filteredKas.reduce((sum, kas) => sum + (kas.stok_awal || 0), 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Initial stock
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Stock Akhir</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {filteredKas.reduce((sum, kas) => sum + (kas.stock_akhir || 0), 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Final stock
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Filters</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label>Search</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by Kang Ider or notes..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Kang Ider</Label>
              <Select value={kangIderFilter} onValueChange={setKangIderFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Kang Ider</SelectItem>
                  {kangIders.map((kangIder) => (
                    <SelectItem key={kangIder.id} value={kangIder.id}>
                      {kangIder.nama}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Date</Label>
              <Input
                type="date"
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
              />
            </div>

            <div className="flex items-end">
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm("")
                  setKangIderFilter("all")
                  setDateFilter("")
                }}
                className="w-full"
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Kas Harian Table */}
      <Card>
        <CardHeader>
          <CardTitle>Kas Harian Records</CardTitle>
          <CardDescription>
            Daily cash management records ({filteredKas.length} records)
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredKas.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No kas harian records found</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Kang Ider</TableHead>
                  <TableHead>Stock Awal</TableHead>
                  <TableHead>Stock Akhir</TableHead>
                  <TableHead>Stock Variance</TableHead>
                  <TableHead>Total Penjualan</TableHead>
                  <TableHead>Petty Cash</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredKas.map((kas) => {
                  const stockVariance = calculateStockVariance(kas.stok_awal || 0, kas.stock_akhir || 0)
                  const stockHarianItems = getStockHarianForKas(kas.id)
                  
                  return (
                    <TableRow key={kas.id}>
                      <TableCell>
                        {format(new Date(kas.tanggal), "dd MMM yyyy")}
                      </TableCell>
                      <TableCell className="font-medium">
                        {getKangIderName(kas.idkangider)}
                      </TableCell>
                      <TableCell>{kas.stok_awal || 0}</TableCell>
                      <TableCell>{kas.stock_akhir || 0}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          {stockVariance.variance > 0 ? (
                            <TrendingUp className="h-4 w-4 text-green-500" />
                          ) : stockVariance.variance < 0 ? (
                            <TrendingDown className="h-4 w-4 text-red-500" />
                          ) : null}
                          <span className={
                            stockVariance.variance > 0 ? "text-green-600" :
                            stockVariance.variance < 0 ? "text-red-600" : ""
                          }>
                            {stockVariance.variance > 0 ? "+" : ""}{stockVariance.variance}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="font-bold">
                        {(kas.total_penjualan || 0).toLocaleString()}
                      </TableCell>
                      <TableCell>
                        {(kas.petty_cash || 0).toLocaleString()}
                      </TableCell>
                      <TableCell>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          View ({stockHarianItems.length})
                        </Button>
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
