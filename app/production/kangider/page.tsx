"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import { 
  ArrowLeft, 
  Plus, 
  Eye, 
  Edit, 
  Truck, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Search,
  Filter
} from "lucide-react"
import { format } from "date-fns"
import { 
  getSalesPlans, 
  getKangIders, 
  getProduk, 
  updateSalesPlanWithWorkflow,
  type SalesPlanned,
  type KangIder,
  type Produk
} from "@/lib/directus"

export default function KangIderDistributionPage() {
  const router = useRouter()
  const { toast } = useToast()
  
  const [isLoading, setIsLoading] = useState(true)
  const [salesPlans, setSalesPlans] = useState<SalesPlanned[]>([])
  const [kangIders, setKangIders] = useState<KangIder[]>([])
  const [products, setProducts] = useState<Produk[]>([])
  const [filteredPlans, setFilteredPlans] = useState<SalesPlanned[]>([])
  
  // Filters
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [kangIderFilter, setKangIderFilter] = useState<string>("all")
  const [dateFilter, setDateFilter] = useState<string>("")

  useEffect(() => {
    async function fetchData() {
      try {
        const [salesPlansData, kangIdersData, productsData] = await Promise.all([
          getSalesPlans(),
          getKangIders(),
          getProduk()
        ])

        setSalesPlans(salesPlansData)
        setKangIders(kangIdersData)
        setProducts(productsData)
        setFilteredPlans(salesPlansData)
      } catch (error) {
        console.error("Error fetching data:", error)
        toast({
          title: "Error",
          description: "Failed to load data",
          variant: "destructive"
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [toast])

  // Apply filters
  useEffect(() => {
    let filtered = salesPlans

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(plan => {
        const kangIder = kangIders.find(k => k.id === plan.kangider)
        return kangIder?.nama.toLowerCase().includes(searchTerm.toLowerCase()) ||
               plan.id.toLowerCase().includes(searchTerm.toLowerCase())
      })
    }

    // Status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(plan => plan.status === statusFilter)
    }

    // KangIder filter
    if (kangIderFilter !== "all") {
      filtered = filtered.filter(plan => plan.kangider === kangIderFilter)
    }

    // Date filter
    if (dateFilter) {
      filtered = filtered.filter(plan => plan.date === dateFilter)
    }

    setFilteredPlans(filtered)
  }, [salesPlans, searchTerm, statusFilter, kangIderFilter, dateFilter, kangIders])

  const handleStatusChange = async (planId: string, newStatus: "draft" | "published" | "load ider") => {
    try {
      console.log(`Updating plan ${planId} to status: ${newStatus}`)
      
      const result = await updateSalesPlanWithWorkflow(planId, { status: newStatus })
      
      // Update local state
      setSalesPlans(prev => prev.map(plan => 
        plan.id === planId ? { ...plan, status: newStatus } : plan
      ))

      let message = `Status updated to ${newStatus}`
      if (newStatus === "load ider" && result.workflow) {
        message += `. Created kas_harian and ${result.workflow.stockHarianRecords.length} stock_harian records.`
      }

      toast({
        title: "Success",
        description: message,
      })
    } catch (error) {
      console.error("Error updating status:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update status",
        variant: "destructive"
      })
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "draft":
        return <Badge variant="outline"><Clock className="h-3 w-3 mr-1" />Draft</Badge>
      case "published":
        return <Badge variant="secondary"><AlertCircle className="h-3 w-3 mr-1" />Published</Badge>
      case "load ider":
        return <Badge variant="default"><CheckCircle className="h-3 w-3 mr-1" />Load Ider</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getKangIderName = (kangIderId: string) => {
    const kangIder = kangIders.find(k => k.id === kangIderId)
    return kangIder?.nama || "Unknown"
  }

  const getTotalItems = (plan: SalesPlanned) => {
    return plan.items?.reduce((sum, item) => sum + item.quantity, 0) || 0
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-lg font-semibold">Loading...</h2>
          <p className="text-muted-foreground">Please wait</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center space-x-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Kang Ider Distribution</h1>
          <p className="text-muted-foreground">
            Manage sales plans and distribution to Kang Ider
          </p>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Filters</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="space-y-2">
              <Label>Search</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by Kang Ider or ID..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="published">Published</SelectItem>
                  <SelectItem value="load ider">Load Ider</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Kang Ider</Label>
              <Select value={kangIderFilter} onValueChange={setKangIderFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Kang Ider</SelectItem>
                  {kangIders.map((kangIder) => (
                    <SelectItem key={kangIder.id} value={kangIder.id}>
                      {kangIder.nama}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Date</Label>
              <Input
                type="date"
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
              />
            </div>

            <div className="flex items-end">
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm("")
                  setStatusFilter("all")
                  setKangIderFilter("all")
                  setDateFilter("")
                }}
                className="w-full"
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sales Plans Table */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Sales Plans</CardTitle>
              <CardDescription>
                Manage distribution plans for Kang Ider ({filteredPlans.length} plans)
              </CardDescription>
            </div>
            <Button asChild>
              <a href="/sales-planning/add">
                <Plus className="h-4 w-4 mr-2" />
                New Sales Plan
              </a>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {filteredPlans.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No sales plans found</p>
              <Button className="mt-4" asChild>
                <a href="/sales-planning/add">
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Sales Plan
                </a>
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Kang Ider</TableHead>
                  <TableHead>Total Items</TableHead>
                  <TableHead>Total Cups</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPlans.map((plan) => (
                  <TableRow key={plan.id}>
                    <TableCell>
                      {format(new Date(plan.date), "dd MMM yyyy")}
                    </TableCell>
                    <TableCell className="font-medium">
                      {getKangIderName(plan.kangider)}
                    </TableCell>
                    <TableCell>
                      {getTotalItems(plan)} items
                    </TableCell>
                    <TableCell className="font-bold">
                      {plan.total_cup} cups
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(plan.status)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button variant="outline" size="sm" asChild>
                          <a href={`/sales-planning/view/${plan.id}`}>
                            <Eye className="h-4 w-4" />
                          </a>
                        </Button>
                        
                        {plan.status === "draft" && (
                          <Button variant="outline" size="sm" asChild>
                            <a href={`/sales-planning/edit/${plan.id}`}>
                              <Edit className="h-4 w-4" />
                            </a>
                          </Button>
                        )}

                        {plan.status === "published" && (
                          <Button 
                            size="sm"
                            onClick={() => handleStatusChange(plan.id, "load ider")}
                          >
                            <Truck className="h-4 w-4 mr-1" />
                            Load Ider
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
