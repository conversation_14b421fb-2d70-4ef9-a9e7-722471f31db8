"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import { createDailyProduction, updateDailyProduction, DailyProduction, Produk } from "@/lib/directus"

const formSchema = z.object({
  produk_id: z.string({
    required_error: "Please select a product",
  }),
  quantity_produced: z.coerce.number().min(1, "Quantity must be at least 1"),
  target_quantity: z.coerce.number().min(1, "Target quantity must be at least 1"),
  notes: z.string().optional(),
})

interface ProductionFormProps {
  products: Produk[]
  initialProductId?: string
  mode: "add" | "edit"
  productionData?: DailyProduction
}

export function ProductionForm({ products, initialProductId, mode, productionData }: ProductionFormProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      produk_id: initialProductId || "",
      quantity_produced: productionData?.quantity_produced || 0,
      target_quantity: productionData?.target_quantity || 0,
      notes: productionData?.notes || "",
    },
  })

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      setIsSubmitting(true)

      const newProductionData = {
        ...values,
        date: new Date().toISOString().split('T')[0]
      }

      if (mode === "add") {
        await createDailyProduction(newProductionData)
        toast({
          title: "Success",
          description: "Production record created successfully",
        })
      } else {
        if (!productionData?.id) {
          throw new Error("Production ID is required for update")
        }
        await updateDailyProduction(productionData.id, newProductionData)
        toast({
          title: "Success",
          description: "Production record updated successfully",
        })
      }

      router.push("/production")
      router.refresh()
    } catch (error) {
      console.error("Error submitting production:", error)
      toast({
        title: "Error",
        description: "Failed to save production record",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">
          {mode === "add" ? "Record Production" : "Edit Production"}
        </h1>
        <p className="text-muted-foreground">
          {mode === "add"
            ? "Record a new production quantity for today"
            : "Update the production record"}
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <FormField
            control={form.control}
            name="produk_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Product</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  value={field.value}
                  defaultValue={initialProductId}
                  disabled={mode === "edit"}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a product" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {products.map((product) => (
                      <SelectItem key={product.id} value={product.id}>
                        {product.nama_produk}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>
                  Select the product that was produced
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="quantity_produced"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Quantity Produced</FormLabel>
                <FormControl>
                  <Input type="number" placeholder="Enter quantity" {...field} />
                </FormControl>
                <FormDescription>
                  Enter the total quantity produced today
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="target_quantity"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Target Quantity</FormLabel>
                <FormControl>
                  <Input type="number" placeholder="Enter target quantity" {...field} />
                </FormControl>
                <FormDescription>
                  Enter the target quantity for today
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Add any notes about the production"
                    className="resize-none"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Add any additional notes or comments
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex gap-4">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Saving..." : mode === "add" ? "Record Production" : "Update Production"}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push("/production")}
            >
              Cancel
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}

