"use client"

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Edit, Plus, Minus } from "lucide-react";
import Link from "next/link";

interface ProductionStockItem {
  id: string;
  produk: string;
  quantity: number;
  last_updated: string;
  product_name: string;
}

interface ProductionStockTableProps {
  stocks: ProductionStockItem[];
}

export function ProductionStockTable({ stocks }: ProductionStockTableProps) {
  return (
    <div>
      {stocks.length === 0 ? (
        <div className="text-center py-10">
          <p className="text-muted-foreground">No production stock found</p>
          <Button className="mt-4" asChild>
            <Link href="/api/setup-production-stock">Setup Production Stock</Link>
          </Button>
        </div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Product</TableHead>
              <TableHead className="text-right">Quantity</TableHead>
              <TableHead>Last Updated</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {stocks.map((stock) => (
              <TableRow key={stock.id}>
                <TableCell className="font-medium">{stock.product_name}</TableCell>
                <TableCell className="text-right">{stock.quantity}</TableCell>
                <TableCell>
                  {new Date(stock.last_updated).toLocaleDateString()}
                </TableCell>
                <TableCell>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="icon" asChild>
                      <Link href={`/production/stock/add?product=${stock.produk}`}>
                        <Plus className="h-4 w-4" />
                      </Link>
                    </Button>
                    <Button variant="outline" size="icon" asChild>
                      <Link href={`/production/stock/subtract?product=${stock.produk}`}>
                        <Minus className="h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}
    </div>
  );
} 