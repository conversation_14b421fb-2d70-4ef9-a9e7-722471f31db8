"use client"

import { useState, useEffect } from "react"
import { useR<PERSON>er, useSearchParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { useToast } from "@/components/ui/use-toast"
import { ArrowLeft, RotateCcw, AlertTriangle, CheckCircle, XCircle } from "lucide-react"
import { getProduk } from "@/lib/directus"
import { getDistributions, createReturn } from "@/lib/production-api"
import type { Produk } from "@/lib/directus"
import type { ProductionDistribution, ReturnFormData } from "@/lib/production-types"

export default function AddReturnPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()
  
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [products, setProducts] = useState<Produk[]>([])
  const [distributions, setDistributions] = useState<ProductionDistribution[]>([])
  const [selectedDistribution, setSelectedDistribution] = useState<ProductionDistribution | null>(null)
  
  const [formData, setFormData] = useState<ReturnFormData>({
    distribution_id: "",
    quantity: 0,
    reason: "",
    condition: "good",
    action: "restock",
    notes: ""
  })

  useEffect(() => {
    async function fetchData() {
      try {
        const distributionId = searchParams.get('distribution')
        
        const [productsData, distributionsData] = await Promise.all([
          getProduk(),
          getDistributions(undefined, undefined, 'distributed') // Only distributed items can be returned
        ])

        setProducts(productsData)
        setDistributions(distributionsData)

        // Pre-select distribution if provided in URL
        if (distributionId) {
          const distribution = distributionsData.find(d => d.id === distributionId)
          if (distribution) {
            setSelectedDistribution(distribution)
            setFormData(prev => ({ ...prev, distribution_id: distributionId }))
          }
        }
      } catch (error) {
        console.error("Error fetching data:", error)
        toast({
          title: "Error",
          description: "Failed to load data",
          variant: "destructive"
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [searchParams, toast])

  const handleDistributionChange = (distributionId: string) => {
    const distribution = distributions.find(d => d.id === distributionId)
    setSelectedDistribution(distribution || null)
    setFormData(prev => ({ 
      ...prev, 
      distribution_id: distributionId,
      quantity: 0 // Reset quantity when distribution changes
    }))
  }

  const getMaxReturnQuantity = () => {
    if (!selectedDistribution) return 0
    return selectedDistribution.quantity - (selectedDistribution.returned_quantity || 0)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validation
    if (!formData.distribution_id) {
      toast({
        title: "Validation Error",
        description: "Please select a distribution",
        variant: "destructive"
      })
      return
    }

    if (formData.quantity <= 0) {
      toast({
        title: "Validation Error",
        description: "Please enter a valid quantity",
        variant: "destructive"
      })
      return
    }

    if (formData.quantity > getMaxReturnQuantity()) {
      toast({
        title: "Validation Error",
        description: `Maximum return quantity is ${getMaxReturnQuantity()}`,
        variant: "destructive"
      })
      return
    }

    if (!formData.reason.trim()) {
      toast({
        title: "Validation Error",
        description: "Please provide a reason for the return",
        variant: "destructive"
      })
      return
    }

    setIsSubmitting(true)

    try {
      await createReturn(formData)

      toast({
        title: "Success",
        description: "Return processed successfully",
      })

      router.push('/production?tab=returns')
    } catch (error) {
      console.error("Error processing return:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to process return",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-lg font-semibold">Loading...</h2>
          <p className="text-muted-foreground">Please wait</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center space-x-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Process Return</h1>
          <p className="text-muted-foreground">
            Handle returned products from distribution channels
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <RotateCcw className="h-5 w-5" />
              <span>Return Information</span>
            </CardTitle>
            <CardDescription>
              Select the distribution and specify return details
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Distribution Selection */}
            <div className="space-y-2">
              <Label>Distribution *</Label>
              <Select
                value={formData.distribution_id}
                onValueChange={handleDistributionChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select distribution to return from" />
                </SelectTrigger>
                <SelectContent>
                  {distributions.map((distribution) => {
                    const product = products.find(p => p.id === distribution.produk_id)
                    const maxReturn = distribution.quantity - (distribution.returned_quantity || 0)
                    return (
                      <SelectItem key={distribution.id} value={distribution.id}>
                        {product?.nama_produk} - {distribution.quantity} units 
                        (Can return: {maxReturn})
                      </SelectItem>
                    )
                  })}
                </SelectContent>
              </Select>
            </div>

            {/* Distribution Details */}
            {selectedDistribution && (
              <div className="p-4 bg-muted rounded-lg">
                <h4 className="font-medium mb-2">Distribution Details</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Product:</span>
                    <p className="font-medium">
                      {products.find(p => p.id === selectedDistribution.produk_id)?.nama_produk}
                    </p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Channel:</span>
                    <p className="font-medium">{selectedDistribution.distribution_channel_id}</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Distributed:</span>
                    <p className="font-medium">{selectedDistribution.quantity} units</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Already Returned:</span>
                    <p className="font-medium">{selectedDistribution.returned_quantity || 0} units</p>
                  </div>
                </div>
              </div>
            )}

            {/* Return Quantity */}
            <div className="space-y-2">
              <Label>Return Quantity *</Label>
              <Input
                type="number"
                min="1"
                max={getMaxReturnQuantity()}
                value={formData.quantity}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  quantity: parseInt(e.target.value) || 0 
                }))}
                placeholder="0"
              />
              <p className="text-sm text-muted-foreground">
                Maximum: {getMaxReturnQuantity()} units
              </p>
            </div>

            {/* Return Reason */}
            <div className="space-y-2">
              <Label>Return Reason *</Label>
              <Textarea
                value={formData.reason}
                onChange={(e) => setFormData(prev => ({ ...prev, reason: e.target.value }))}
                placeholder="Why are these items being returned?"
                rows={3}
              />
            </div>

            {/* Product Condition */}
            <div className="space-y-3">
              <Label>Product Condition *</Label>
              <RadioGroup
                value={formData.condition}
                onValueChange={(value: 'good' | 'damaged' | 'expired') => 
                  setFormData(prev => ({ ...prev, condition: value }))
                }
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="good" id="good" />
                  <Label htmlFor="good" className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Good - Can be resold</span>
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="damaged" id="damaged" />
                  <Label htmlFor="damaged" className="flex items-center space-x-2">
                    <AlertTriangle className="h-4 w-4 text-yellow-500" />
                    <span>Damaged - Needs assessment</span>
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="expired" id="expired" />
                  <Label htmlFor="expired" className="flex items-center space-x-2">
                    <XCircle className="h-4 w-4 text-red-500" />
                    <span>Expired - Cannot be resold</span>
                  </Label>
                </div>
              </RadioGroup>
            </div>

            {/* Action to Take */}
            <div className="space-y-3">
              <Label>Action to Take *</Label>
              <RadioGroup
                value={formData.action}
                onValueChange={(value: 'restock' | 'dispose' | 'redistribute') => 
                  setFormData(prev => ({ ...prev, action: value }))
                }
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="restock" id="restock" />
                  <Label htmlFor="restock">Restock - Add back to available inventory</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="dispose" id="dispose" />
                  <Label htmlFor="dispose">Dispose - Remove from inventory</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="redistribute" id="redistribute" />
                  <Label htmlFor="redistribute">Redistribute - Hold for redistribution</Label>
                </div>
              </RadioGroup>
            </div>

            {/* Additional Notes */}
            <div className="space-y-2">
              <Label>Additional Notes</Label>
              <Textarea
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Any additional information about this return..."
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting || !selectedDistribution}>
            {isSubmitting ? "Processing..." : "Process Return"}
          </Button>
        </div>
      </form>
    </div>
  )
}
