import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import directus from "@/lib/directus";
import { readItems } from "@directus/sdk";
import { ProductionStockTable } from "../production-stock-table";

export const dynamic = 'force-dynamic';

async function fetchProductionStock() {
  try {
    // Get all stock items with product details
    const stock = await directus.request(
      readItems("production_stock", {
        fields: ["*"],
        limit: -1
      })
    );
    
    // Get all products to merge with stock
    const products = await directus.request(
      readItems("produk", {
        fields: ["id", "nama_produk"],
        filter: { status: { _eq: "published" } }
      })
    );
    
    // Merge product details with stock
    const stockWithDetails = stock.map(item => {
      const product = products.find(p => String(p.id) === String(item.produk));
      return {
        ...item,
        product_name: product?.nama_produk || `Product ${item.produk}`
      };
    });
    
    return stockWithDetails;
  } catch (error) {
    console.error("Error fetching production stock:", error);
    return [];
  }
}

export default async function ProductionStockPage() {
  const stockItems = await fetchProductionStock();
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Production Stock</h1>
          <p className="text-muted-foreground">Manage your production inventory</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <a href="/api/setup-production-stock" target="_blank">Setup Stock</a>
          </Button>
          <Button asChild>
            <a href="/production/stock/add">Add Stock</a>
          </Button>
        </div>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Current Production Stock</CardTitle>
          <CardDescription>View and manage your current production inventory</CardDescription>
        </CardHeader>
        <CardContent>
          <ProductionStockTable stocks={stockItems} />
        </CardContent>
      </Card>
    </div>
  );
} 