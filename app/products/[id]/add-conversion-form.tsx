"use client"

import { useState } from "react"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { But<PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "@/hooks/use-toast"
import { Calculator, X } from "lucide-react"
import { addConversionUnit } from "@/app/actions"

const formSchema = z.object({
  unit_name: z.string().min(1, {
    message: "Unit name is required",
  }),
  conversion_to_base: z.coerce.number().positive({
    message: "Conversion factor must be a positive number",
  }),
})

interface AddConversionFormProps {
  productId: string
  baseUnit: string
  onSuccess: () => void
  onCancel: () => void
}

export function AddConversionForm({ productId, baseUnit, onSuccess, onCancel }: AddConversionFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      unit_name: "",
      conversion_to_base: 1,
    },
  })

  const watchedConversion = form.watch("conversion_to_base")
  const watchedUnitName = form.watch("unit_name")

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsSubmitting(true)
    try {
      const result = await addConversionUnit({
        unit_name: values.unit_name,
        conversion_to_base: values.conversion_to_base,
        id_produk_gudang: productId,
      })
      
      if (result.success) {
        toast({
          title: "Success",
          description: "Conversion unit added successfully",
        })
        onSuccess()
      } else {
        toast({
          title: "Error",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add conversion unit",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              Add Conversion Unit
            </CardTitle>
            <CardDescription>
              Define how this unit converts to the base unit ({baseUnit})
            </CardDescription>
          </div>
          <Button variant="ghost" size="sm" onClick={onCancel}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="unit_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Unit Name</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., LITER, BOX, DOZEN" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="conversion_to_base"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Conversion Factor</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        step="0.01" 
                        placeholder="e.g., 1000" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Preview */}
            {watchedUnitName && watchedConversion > 0 && (
              <div className="p-4 bg-muted rounded-lg">
                <h4 className="font-medium mb-2">Conversion Preview:</h4>
                <div className="space-y-1 text-sm">
                  <div>1 {watchedUnitName} = {watchedConversion} {baseUnit}</div>
                  <div>10 {watchedUnitName} = {10 * watchedConversion} {baseUnit}</div>
                  <div>100 {watchedUnitName} = {100 * watchedConversion} {baseUnit}</div>
                </div>
              </div>
            )}

            <div className="text-sm text-muted-foreground">
              <strong>Example:</strong> If your base unit is "ML" and you want to add "LITER", 
              the conversion factor would be 1000 (because 1 LITER = 1000 ML).
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" type="button" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Adding..." : "Add Conversion"}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  )
}
