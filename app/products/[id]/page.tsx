"use client"

import { useEffect, useState, use } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { Button } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useToast } from "@/components/ui/use-toast"
import { ArrowDownCircle, ArrowUpCircle, Trash2, Plus, Calculator } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { getProduct, updateProduct, deleteProduct, getStockMovements, getCurrentStock } from "@/lib/directus"
import type { ProdukGudang, StockMovement, CurrentStock, ProdukGudangConversion } from "@/lib/directus"
import { fetchConversionUnits, addConversionUnit, removeConversionUnit } from "@/app/actions"
import { AddConversionForm } from "./add-conversion-form"

const formSchema = z.object({
  name: z.string().min(2, {
    message: "Name must be at least 2 characters.",
  }),
  category: z.string().min(2, {
    message: "Category must be at least 2 characters.",
  }),
  description: z.string().optional(),
  sku: z.string().min(3, {
    message: "SKU must be at least 3 characters.",
  }),
  unit: z.string().min(1, {
    message: "Unit is required.",
  }),
  purchase_price: z.union([
    z.string().length(0),
    z.string().transform((val) => parseFloat(val)).pipe(z.number().positive())
  ]).optional(),
  min_stock: z.coerce.number().min(0, {
    message: "Minimum stock cannot be negative.",
  }),
})

export default function ProductDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params)
  const router = useRouter()
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  const [product, setProduct] = useState<ProdukGudang | null>(null)
  const [movements, setMovements] = useState<StockMovement[]>([])
  const [currentStock, setCurrentStock] = useState<number>(0)
  const [conversionUnits, setConversionUnits] = useState<ProdukGudangConversion[]>([])
  const [isLoadingConversions, setIsLoadingConversions] = useState(false)
  const [showAddConversion, setShowAddConversion] = useState(false)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      category: "",
      description: "",
      sku: "",
      unit: "",
      purchase_price: "",
      min_stock: 0,
    },
  })

  useEffect(() => {
    if (!resolvedParams?.id) return

    async function fetchData() {
      try {
        const [productData] = await getProduct(resolvedParams.id)
        if (productData) {
          setProduct(productData)
          form.reset({
            name: productData.name,
            category: productData.category,
            description: productData.description || "",
            sku: productData.sku,
            unit: productData.unit,
            purchase_price: productData.purchase_price?.toString() || "",
            min_stock: productData.min_stock,
          })

          const stockData = await getCurrentStock()
          const productStock = stockData.find(
            (stock: CurrentStock) => stock.produk_gudang_id === resolvedParams.id
          )
          setCurrentStock(productStock?.current_stock || 0)
        }

        // --- Fetch stock movements ---
        const movementsData = await getStockMovements({
          produk_gudang_id: { _eq: resolvedParams.id }
        })
        setMovements(movementsData as StockMovement[])

        // --- Calculate stock ---
        const totalIncoming = movementsData
          .filter(m => m.type === 'incoming')
          .reduce((sum, m) => sum + m.quantity, 0)

        const totalOutgoing = movementsData
          .filter(m => m.type === 'outgoing')
          .reduce((sum, m) => sum + m.quantity, 0)

        setCurrentStock(totalIncoming - totalOutgoing)

        // --- Fetch conversion units ---
        await loadConversionUnits()

      } catch (error) {
        console.error("Error fetching product:", error)
        toast({
          title: "Error",
          description: "Failed to load product data. Please try again.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [resolvedParams.id, form, toast])

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsSubmitting(true)

    try {
      // Prepare data for update
      const updateData = {
        ...values,
        purchase_price: values.purchase_price || undefined
      }

      await updateProduct(resolvedParams.id, updateData)

      toast({
        title: "Product updated",
        description: `${values.name} has been updated.`,
      })

      // Update local state
      setProduct(prev => prev ? { ...prev, ...updateData } : null)

    } catch (error) {
      console.error('Error updating product:', error)
      toast({
        title: "Error",
        description: "Failed to update product. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  async function handleDelete() {
    setIsDeleting(true)

    try {
      await deleteProduct(resolvedParams.id)

      toast({
        title: "Product deleted",
        description: `${product?.name} has been deleted.`,
      })

      router.push("/products")
    } catch (error) {
      console.error('Error deleting product:', error)
      toast({
        title: "Error",
        description: "Failed to delete product. Please try again.",
        variant: "destructive",
      })
      setIsDeleting(false)
    }
  }

  async function loadConversionUnits() {
    if (!resolvedParams?.id) return

    setIsLoadingConversions(true)
    try {
      const result = await fetchConversionUnits(resolvedParams.id)
      if (result.success) {
        setConversionUnits(result.data)
      }
    } catch (error) {
      console.error("Error loading conversion units:", error)
    } finally {
      setIsLoadingConversions(false)
    }
  }

  async function handleDeleteConversion(conversionId: string) {
    try {
      const result = await removeConversionUnit(conversionId, resolvedParams.id)
      if (result.success) {
        toast({
          title: "Success",
          description: "Conversion unit deleted successfully",
        })
        await loadConversionUnits()
      } else {
        toast({
          title: "Error",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete conversion unit",
        variant: "destructive",
      })
    }
  }

  if (isLoading || !product) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-lg font-semibold">Loading...</h2>
          <p className="text-muted-foreground">Please wait while we fetch the product details.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{product.name}</h1>
          <p className="text-muted-foreground">
            SKU: {product.sku} • Category: {product.category}
          </p>
        </div>
        <div className="flex gap-2">
          <Button asChild>
            <a href={`/stock/add-movement?product=${product.id}`}>Record Movement</a>
          </Button>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive">
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action cannot be undone. This will permanently delete the product and all associated stock
                  movements.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleDelete}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  disabled={isDeleting}
                >
                  {isDeleting ? "Deleting..." : "Delete"}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-6">
        <Card className="md:col-span-4">
          <CardHeader>
            <CardTitle>Current Stock</CardTitle>
            <CardDescription>Current stock level and status</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4 items-center">
              <div className="text-center md:text-left">
                <div className="text-sm font-medium text-muted-foreground">Current Stock</div>
                <div className="text-4xl font-bold">{currentStock}</div>
                <div className="text-sm text-muted-foreground">{product.unit} (Base Unit)</div>

                {/* Multi-unit display */}
                {conversionUnits.length > 0 && (
                  <div className="mt-2 space-y-1">
                    <div className="text-xs text-muted-foreground">Available in:</div>
                    {conversionUnits.slice(0, 2).map((conversion) => (
                      <div key={conversion.id} className="text-xs text-muted-foreground">
                        ≈ {(currentStock / conversion.conversion_to_base).toFixed(2)} {conversion.unit_name}
                      </div>
                    ))}
                    {conversionUnits.length > 2 && (
                      <div className="text-xs text-muted-foreground">
                        +{conversionUnits.length - 2} more units
                      </div>
                    )}
                  </div>
                )}
              </div>

              <div className="text-center md:text-left md:ml-12">
                <div className="text-sm font-medium text-muted-foreground">Minimum Stock</div>
                <div className="text-2xl font-medium">{product.min_stock}</div>
                <div className="text-sm text-muted-foreground">{product.unit}</div>
              </div>

              <div className="md:ml-auto">
                {currentStock < product.min_stock ? (
                  <div className="text-center">
                    <Badge variant="destructive" className="mb-2 text-base py-1 px-3">
                      Low Stock
                    </Badge>
                    <div className="text-sm text-muted-foreground">
                      {product.min_stock - currentStock} units below minimum
                    </div>
                  </div>
                ) : (
                  <div className="text-center">
                    <Badge variant="outline" className="mb-2 text-base py-1 px-3 border-green-500 text-green-500">
                      In Stock
                    </Badge>
                    <div className="text-sm text-muted-foreground">
                      {currentStock - product.min_stock} units above minimum
                    </div>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button className="w-full" asChild>
              <a href={`/stock/add-movement?product=${product.id}&type=incoming`}>
                <ArrowDownCircle className="mr-2 h-4 w-4" />
                Stock In
              </a>
            </Button>
            <Button className="w-full" variant="outline" asChild>
              <a href={`/stock/add-movement?product=${product.id}&type=outgoing`}>
                <ArrowUpCircle className="mr-2 h-4 w-4" />
                Stock Out
              </a>
            </Button>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="details">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="details">Product Details</TabsTrigger>
          <TabsTrigger value="conversions">Unit Conversions</TabsTrigger>
          <TabsTrigger value="movements">Stock Movements</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Edit Product</CardTitle>
              <CardDescription>Update the product details</CardDescription>
            </CardHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)}>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Name</FormLabel>
                          <FormControl>
                            <Input placeholder="Product name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="category"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Category</FormLabel>
                          <FormControl>
                            <Input placeholder="Category" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea placeholder="Product description (optional)" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="sku"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>SKU</FormLabel>
                          <FormControl>
                            <Input placeholder="Stock keeping unit" {...field} />
                          </FormControl>
                          <FormDescription>Unique identifier for this product</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="unit"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Unit</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g., pcs, kg, box" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="purchase_price"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Purchase Price</FormLabel>
                          <FormControl>
                            <Input type="number" step="0.01" placeholder="0.00" {...field} />
                          </FormControl>
                          <FormDescription>Default purchase price per unit (optional)</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="min_stock"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Minimum Stock</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormDescription>Alert when stock falls below this level</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
                <CardFooter>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? "Saving..." : "Save Changes"}
                  </Button>
                </CardFooter>
              </form>
            </Form>
          </Card>
        </TabsContent>

        <TabsContent value="conversions" className="space-y-4">
          {showAddConversion && (
            <AddConversionForm
              productId={product.id!}
              baseUnit={product.unit}
              onSuccess={() => {
                setShowAddConversion(false)
                loadConversionUnits()
              }}
              onCancel={() => setShowAddConversion(false)}
            />
          )}

          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Calculator className="h-5 w-5" />
                    Unit Conversions
                  </CardTitle>
                  <CardDescription>
                    Manage different units for this product. Base unit: {product.unit}
                  </CardDescription>
                </div>
                <Button onClick={() => setShowAddConversion(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Conversion
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {isLoadingConversions ? (
                <div className="text-center py-4">Loading conversion units...</div>
              ) : conversionUnits.length === 0 ? (
                <div className="text-center py-8">
                  <Calculator className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-2 text-sm font-semibold text-muted-foreground">No conversion units</h3>
                  <p className="mt-1 text-sm text-muted-foreground">
                    Add conversion units to allow multi-unit stock management.
                  </p>
                  <div className="mt-6">
                    <Button onClick={() => setShowAddConversion(true)}>
                      <Plus className="mr-2 h-4 w-4" />
                      Add Conversion
                    </Button>
                  </div>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Unit Name</TableHead>
                      <TableHead>Conversion to Base</TableHead>
                      <TableHead>Example</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {conversionUnits.map((conversion) => (
                      <TableRow key={conversion.id}>
                        <TableCell className="font-medium">{conversion.unit_name}</TableCell>
                        <TableCell>1 {conversion.unit_name} = {conversion.conversion_to_base} {product.unit}</TableCell>
                        <TableCell className="text-muted-foreground">
                          10 {conversion.unit_name} = {10 * conversion.conversion_to_base} {product.unit}
                        </TableCell>
                        <TableCell className="text-right">
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="outline" size="sm">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Delete Conversion Unit</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to delete the conversion for "{conversion.unit_name}"?
                                  This action cannot be undone.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDeleteConversion(conversion.id)}
                                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                >
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="movements" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Stock Movement History</CardTitle>
              <CardDescription>All stock movements for this product</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Quantity (Base Unit)</TableHead>
                    <TableHead>Original Input</TableHead>
                    <TableHead>Purchase Price</TableHead>
                    <TableHead>Total Cost</TableHead>
                    <TableHead>Vendor</TableHead>
                    <TableHead>Reference</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {movements.map((movement) => (
                    <TableRow key={movement.id}>
                      <TableCell>{new Date(movement.date).toLocaleDateString()}</TableCell>
                      <TableCell>
                        {movement.type === "incoming" ? (
                          <div className="flex items-center gap-1">
                            <ArrowDownCircle className="h-4 w-4 text-green-500" />
                            <span>Incoming</span>
                          </div>
                        ) : (
                          <div className="flex items-center gap-1">
                            <ArrowUpCircle className="h-4 w-4 text-red-500" />
                            <span>Outgoing</span>
                          </div>
                        )}
                      </TableCell>
                      <TableCell>{movement.quantity} {product.unit}</TableCell>
                      <TableCell>
                        {movement.quantity_in_unit_used && movement.unit_used ? (
                          <span className="text-muted-foreground">
                            {movement.quantity_in_unit_used} {movement.unit_used}
                          </span>
                        ) : (
                          <span className="text-muted-foreground">
                            {movement.quantity} {product.unit}
                          </span>
                        )}
                      </TableCell>
                      <TableCell>
                        {movement.purchase_price ? (
                          <span className="text-muted-foreground">
                            Rp {movement.purchase_price.toLocaleString('id-ID', { minimumFractionDigits: 2 })}
                          </span>
                        ) : (
                          "-"
                        )}
                      </TableCell>
                      <TableCell>
                        {movement.total_cost ? (
                          <span className="font-medium">
                            Rp {movement.total_cost.toLocaleString('id-ID', { minimumFractionDigits: 2 })}
                          </span>
                        ) : (
                          "-"
                        )}
                      </TableCell>
                      <TableCell>
                        {movement.vendor_id ? (
                          <span className="text-muted-foreground">Vendor ID: {movement.vendor_id}</span>
                        ) : (
                          "-"
                        )}
                      </TableCell>
                      <TableCell>{movement.reference_number || "-"}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

