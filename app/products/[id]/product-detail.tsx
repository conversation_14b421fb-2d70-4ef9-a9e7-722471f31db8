"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { toast } from "@/hooks/use-toast"
import { ArrowDownCircle, ArrowUpCircle, Trash2 } from "lucide-react"
import { <PERSON>ge } from "@/components/ui/badge"
import {
  Alert<PERSON><PERSON>og,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import type { Product, StockMovement } from "@/lib/directus"
import { editProduct, removeProduct } from "@/app/actions"
import Link from "next/link"

const formSchema = z.object({
  name: z.string().min(2, {
    message: "Name must be at least 2 characters.",
  }),
  category: z.string().min(2, {
    message: "Category must be at least 2 characters.",
  }),
  description: z.string().optional(),
  sku: z.string().min(3, {
    message: "SKU must be at least 3 characters.",
  }),
  unit: z.string().min(1, {
    message: "Unit is required.",
  }),
  min_stock: z.coerce.number().min(0, {
    message: "Minimum stock cannot be negative.",
  }),
})

interface ProductDetailProps {
  product: Product
  movements: StockMovement[]
}

export function ProductDetail({ product, movements }: ProductDetailProps) {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  // Calculate current stock from movements
  const totalIncoming = movements.filter((m) => m.type === "incoming").reduce((sum, m) => sum + m.quantity, 0)

  const totalOutgoing = movements.filter((m) => m.type === "outgoing").reduce((sum, m) => sum + m.quantity, 0)

  const currentStock = totalIncoming - totalOutgoing

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: product.name,
      category: product.category,
      description: product.description || "",
      sku: product.sku,
      unit: product.unit,
      min_stock: product.min_stock,
    },
  })

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsSubmitting(true)

    try {
      const result = await editProduct(product.id!, values)

      if (result.success) {
        toast({
          title: "Product updated",
          description: `${values.name} has been updated.`,
        })
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update product. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  async function handleDelete() {
    setIsDeleting(true)

    try {
      const result = await removeProduct(product.id!)

      if (result.success) {
        toast({
          title: "Product deleted",
          description: `${product.name} has been deleted.`,
        })

        router.push("/products")
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete product. Please try again.",
        variant: "destructive",
      })
      setIsDeleting(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{product.name}</h1>
          <p className="text-muted-foreground">
            SKU: {product.sku} • Category: {product.category}
          </p>
        </div>
        <div className="flex gap-2">
          <Button asChild>
            <Link href={`/stock/add-movement?product=${product.id}`}>Record Movement</Link>
          </Button>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive">
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action cannot be undone. This will permanently delete the product and all associated stock
                  movements.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleDelete}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  disabled={isDeleting}
                >
                  {isDeleting ? "Deleting..." : "Delete"}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-6">
        <Card className="md:col-span-4">
          <CardHeader>
            <CardTitle>Current Stock</CardTitle>
            <CardDescription>Current stock level and status</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4 items-center">
              <div className="text-center md:text-left">
                <div className="text-sm font-medium text-muted-foreground">Current Stock</div>
                <div className="text-4xl font-bold">{currentStock}</div>
                <div className="text-sm text-muted-foreground">{product.unit}</div>
              </div>

              <div className="text-center md:text-left md:ml-12">
                <div className="text-sm font-medium text-muted-foreground">Minimum Stock</div>
                <div className="text-2xl font-medium">{product.min_stock}</div>
                <div className="text-sm text-muted-foreground">{product.unit}</div>
              </div>

              <div className="md:ml-auto">
                {currentStock < product.min_stock ? (
                  <div className="text-center">
                    <Badge variant="destructive" className="mb-2 text-base py-1 px-3">
                      Low Stock
                    </Badge>
                    <div className="text-sm text-muted-foreground">
                      {product.min_stock - currentStock} units below minimum
                    </div>
                  </div>
                ) : (
                  <div className="text-center">
                    <Badge variant="outline" className="mb-2 text-base py-1 px-3 border-green-500 text-green-500">
                      In Stock
                    </Badge>
                    <div className="text-sm text-muted-foreground">
                      {currentStock - product.min_stock} units above minimum
                    </div>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button className="w-full" asChild>
              <Link href={`/stock/add-movement?product=${product.id}&type=incoming`}>
                <ArrowDownCircle className="mr-2 h-4 w-4" />
                Stock In
              </Link>
            </Button>
            <Button className="w-full" variant="outline" asChild>
              <Link href={`/stock/add-movement?product=${product.id}&type=outgoing`}>
                <ArrowUpCircle className="mr-2 h-4 w-4" />
                Stock Out
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="details">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="details">Product Details</TabsTrigger>
          <TabsTrigger value="movements">Stock Movements</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Edit Product</CardTitle>
              <CardDescription>Update the product details</CardDescription>
            </CardHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)}>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Name</FormLabel>
                          <FormControl>
                            <Input placeholder="Product name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="category"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Category</FormLabel>
                          <FormControl>
                            <Input placeholder="Category" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea placeholder="Product description (optional)" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="sku"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>SKU</FormLabel>
                          <FormControl>
                            <Input placeholder="Stock keeping unit" {...field} />
                          </FormControl>
                          <FormDescription>Unique identifier for this product</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="unit"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Unit</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g., pcs, kg, box" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="min_stock"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Minimum Stock</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormDescription>Alert when stock falls below this level</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
                <CardFooter>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? "Saving..." : "Save Changes"}
                  </Button>
                </CardFooter>
              </form>
            </Form>
          </Card>
        </TabsContent>

        <TabsContent value="movements" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Stock Movement History</CardTitle>
              <CardDescription>All stock movements for this product</CardDescription>
            </CardHeader>
            <CardContent>
              {movements.length === 0 ? (
                <p className="text-muted-foreground text-center py-4">No stock movements recorded for this product.</p>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Quantity</TableHead>
                      <TableHead>Reference</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {movements.map((movement) => (
                      <TableRow key={movement.id}>
                        <TableCell>{new Date(movement.date).toLocaleDateString()}</TableCell>
                        <TableCell>
                          {movement.type === "incoming" ? (
                            <div className="flex items-center gap-1">
                              <ArrowDownCircle className="h-4 w-4 text-green-500" />
                              <span>Incoming</span>
                            </div>
                          ) : (
                            <div className="flex items-center gap-1">
                              <ArrowUpCircle className="h-4 w-4 text-red-500" />
                              <span>Outgoing</span>
                            </div>
                          )}
                        </TableCell>
                        <TableCell>{movement.quantity}</TableCell>
                        <TableCell>{movement.reference_number || "-"}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

