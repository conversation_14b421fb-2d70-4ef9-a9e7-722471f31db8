"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Upload, FileSpreadsheet, CheckCircle, XCircle, ArrowLeft, Download } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { ProtectedRoute } from '@/components/auth/protected-route'

interface ImportResult {
  message: string
  total: number
  success: number
  errors: number
  results: {
    success: number
    errors: string[]
    products: Array<{
      row: number
      name: string
      sku: string
      productId: string
      conversions: number
      initialStock: number
    }>
  }
}

export default function ImportProductsPage() {
  const [file, setFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [result, setResult] = useState<ImportResult | null>(null)
  const router = useRouter()
  const { toast } = useToast()

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {
      // Validate file type
      const validTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel'
      ]

      if (!validTypes.includes(selectedFile.type)) {
        toast({
          title: "Invalid file type",
          description: "Please select an Excel file (.xlsx or .xls)",
          variant: "destructive"
        })
        return
      }

      setFile(selectedFile)
      setResult(null)
    }
  }

  const handleUpload = async () => {
    if (!file) {
      toast({
        title: "No file selected",
        description: "Please select an Excel file to import",
        variant: "destructive"
      })
      return
    }

    setIsUploading(true)
    setUploadProgress(0)

    try {
      const formData = new FormData()
      formData.append('file', file)

      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return prev
          }
          return prev + 10
        })
      }, 200)

      // Get token from localStorage
      const token = localStorage.getItem('directus_token')
      if (!token) {
        throw new Error('Authentication token not found. Please login again.')
      }

      const response = await fetch('/api/products/import', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      })

      clearInterval(progressInterval)
      setUploadProgress(100)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.details || 'Import failed')
      }

      const data: ImportResult = await response.json()
      setResult(data)

      toast({
        title: "Import completed",
        description: `Successfully imported ${data.success} out of ${data.total} products`,
        variant: data.errors > 0 ? "destructive" : "default"
      })

    } catch (error) {
      console.error('Import error:', error)
      toast({
        title: "Import failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive"
      })
    } finally {
      setIsUploading(false)
      setUploadProgress(0)
    }
  }

  const downloadTemplate = async () => {
    try {
      // Import XLSX dynamically for client-side use
      const XLSX = await import('xlsx')

      // Create a sample Excel template
      const templateData = [
        {
          'No.': 1,
          'Kategori Barang': 'Beverages',
          'Kode Barang': 'AIR001',
          'Nama Barang': 'Air Mineral 600ml',
          'Keterangan': 'Air mineral kemasan botol',
          'Kuantitas': 100,
          'Satuan': 'BOTOL',
          'Satuan #2': 'DUS',
          'Rasio Satuan #2': 24,
          'Satuan #3': 'KARTON',
          'Rasio Satuan #3': 48,
          'Harga Jual': 3000,
          'Harga Beli': 2000,
          'Satuan Beli': 'BOTOL',
          'ID Pemasok': 'V001',
          'Pemasok': 'PT. Aqua Golden Mississippi'
        }
      ]

      // Create workbook and worksheet
      const workbook = XLSX.utils.book_new()
      const worksheet = XLSX.utils.json_to_sheet(templateData)

      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Products')

      // Generate Excel file and download
      XLSX.writeFile(workbook, 'product_import_template.xlsx')

      toast({
        title: "Template downloaded",
        description: "Excel template has been downloaded successfully",
      })
    } catch (error) {
      console.error('Error downloading template:', error)
      toast({
        title: "Download failed",
        description: "Failed to download template",
        variant: "destructive"
      })
    }
  }

  return (
    <ProtectedRoute>
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.back()}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold">Import Products</h1>
              <p className="text-muted-foreground">
                Import products from Excel file with automatic conversion units and initial stock
              </p>
            </div>
          </div>
          <Button variant="outline" onClick={downloadTemplate}>
            <Download className="h-4 w-4 mr-2" />
            Download Template
          </Button>
        </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Upload Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Upload className="h-5 w-5" />
              <span>Upload Excel File</span>
            </CardTitle>
            <CardDescription>
              Select an Excel file (.xlsx or .xls) containing product data
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="file">Excel File</Label>
              <Input
                id="file"
                type="file"
                accept=".xlsx,.xls"
                onChange={handleFileChange}
                disabled={isUploading}
              />
            </div>

            {file && (
              <div className="flex items-center space-x-2 p-3 bg-muted rounded-lg">
                <FileSpreadsheet className="h-5 w-5 text-green-600" />
                <div className="flex-1">
                  <p className="text-sm font-medium">{file.name}</p>
                  <p className="text-xs text-muted-foreground">
                    {(file.size / 1024).toFixed(1)} KB
                  </p>
                </div>
              </div>
            )}

            {isUploading && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Uploading...</span>
                  <span>{uploadProgress}%</span>
                </div>
                <Progress value={uploadProgress} />
              </div>
            )}

            <Button
              onClick={handleUpload}
              disabled={!file || isUploading}
              className="w-full"
            >
              {isUploading ? "Importing..." : "Import Products"}
            </Button>
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Excel Format Requirements</CardTitle>
            <CardDescription>
              Your Excel file should contain the following columns:
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div className="grid grid-cols-2 gap-2">
                <Badge variant="outline">No.</Badge>
                <span>Row number</span>
                <Badge variant="outline">Kategori Barang</Badge>
                <span>Product category</span>
                <Badge variant="outline">Kode Barang</Badge>
                <span>Product SKU (required)</span>
                <Badge variant="outline">Nama Barang</Badge>
                <span>Product name (required)</span>
                <Badge variant="outline">Keterangan</Badge>
                <span>Description</span>
                <Badge variant="outline">Kuantitas</Badge>
                <span>Initial stock quantity</span>
                <Badge variant="outline">Satuan</Badge>
                <span>Base unit (required)</span>
                <Badge variant="outline">Satuan #2</Badge>
                <span>Conversion unit 2</span>
                <Badge variant="outline">Rasio Satuan #2</Badge>
                <span>Conversion ratio 2</span>
                <Badge variant="outline">Satuan #3</Badge>
                <span>Conversion unit 3</span>
                <Badge variant="outline">Rasio Satuan #3</Badge>
                <span>Conversion ratio 3</span>
                <Badge variant="outline">Harga Jual</Badge>
                <span>Selling price</span>
                <Badge variant="outline">Harga Beli</Badge>
                <span>Purchase price</span>
                <Badge variant="outline">Satuan Beli</Badge>
                <span>Purchase unit</span>
                <Badge variant="outline">ID Pemasok</Badge>
                <span>Vendor ID</span>
                <Badge variant="outline">Pemasok</Badge>
                <span>Vendor name</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Results Section */}
      {result && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              {result.errors > 0 ? (
                <XCircle className="h-5 w-5 text-red-500" />
              ) : (
                <CheckCircle className="h-5 w-5 text-green-500" />
              )}
              <span>Import Results</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{result.total}</div>
                <div className="text-sm text-muted-foreground">Total Rows</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{result.success}</div>
                <div className="text-sm text-muted-foreground">Successful</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{result.errors}</div>
                <div className="text-sm text-muted-foreground">Errors</div>
              </div>
            </div>

            {result.results.errors.length > 0 && (
              <>
                <Separator />
                <div>
                  <h4 className="font-medium mb-2">Errors:</h4>
                  <div className="space-y-1">
                    {result.results.errors.map((error, index) => (
                      <Alert key={index} variant="destructive">
                        <AlertDescription className="text-xs">{error}</AlertDescription>
                      </Alert>
                    ))}
                  </div>
                </div>
              </>
            )}

            {result.results.products.length > 0 && (
              <>
                <Separator />
                <div>
                  <h4 className="font-medium mb-2">Successfully Imported Products:</h4>
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {result.results.products.map((product, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                        <div>
                          <div className="font-medium">{product.name}</div>
                          <div className="text-xs text-muted-foreground">
                            SKU: {product.sku} | Conversions: {product.conversions} | Initial Stock: {product.initialStock}
                          </div>
                        </div>
                        <Badge variant="outline">Row {product.row}</Badge>
                      </div>
                    ))}
                  </div>
                </div>
              </>
            )}

            <div className="flex space-x-2">
              <Button onClick={() => router.push('/products')} className="flex-1">
                View Products
              </Button>
              <Button onClick={() => router.push('/stock')} variant="outline" className="flex-1">
                View Stock
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
      </div>
    </ProtectedRoute>
  )
}