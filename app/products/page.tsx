'use client'

import Link from "next/link"
import { Plus, Pencil, Trash2, Upload, Loader2, Search, Filter, Package, TrendingUp, TrendingDown } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { getProducts, getCurrentStock } from "@/lib/directus"
import { useState, useEffect } from "react"
import { ProtectedRoute } from '@/components/auth/protected-route'
import { usePagination } from "@/hooks/use-pagination"
import { PaginationControls } from "@/components/ui/pagination-controls"
import type { ProdukGudang, CurrentStock } from "@/lib/directus"

export default function ProductsPage() {
  const [products, setProducts] = useState<ProdukGudang[]>([])
  const [currentStocks, setCurrentStocks] = useState<Record<string, number>>({})
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Search and filter states
  const [searchTerm, setSearchTerm] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [stockFilter, setStockFilter] = useState("all")
  const [sortBy, setSortBy] = useState("name")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc")
  const [categories, setCategories] = useState<string[]>([])

  useEffect(() => {
    async function fetchData() {
      try {
        setIsLoading(true)
        const [productsData, stocksData] = await Promise.all([
          getProducts(),
          getCurrentStock()
        ])

        // Create a map of product ID to current stock
        const stockMap: Record<string, number> = {}
        stocksData.forEach((stock: CurrentStock) => {
          if (stock.produk_gudang_id) {
            stockMap[stock.produk_gudang_id] = stock.current_stock
          }
        })

        setProducts(productsData)
        setCurrentStocks(stockMap)

        // Extract unique categories
        const uniqueCategories = Array.from(new Set(productsData.map(p => p.category).filter(Boolean)))
        setCategories(uniqueCategories)
      } catch (err: any) {
        console.error("Failed to fetch data:", err)
        setError(err.message || "Failed to load products data")
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  // Format number with thousand separators
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('id-ID').format(num)
  }

  // Filter and sort products
  const filteredAndSortedProducts = products
    .filter(product => {
      // Search filter
      const searchMatch = !searchTerm ||
        product.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.sku?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.category?.toLowerCase().includes(searchTerm.toLowerCase())

      // Category filter
      const categoryMatch = categoryFilter === "all" || product.category === categoryFilter

      // Stock filter
      let stockMatch = true
      if (stockFilter !== "all") {
        const stockStatus = getStockStatus(product.id, product.min_stock)
        stockMatch = stockStatus.status === stockFilter
      }

      return searchMatch && categoryMatch && stockMatch
    })
    .sort((a, b) => {
      let comparison = 0
      switch (sortBy) {
        case "name":
          comparison = (a.name || "").localeCompare(b.name || "")
          break
        case "category":
          comparison = (a.category || "").localeCompare(b.category || "")
          break
        case "sku":
          comparison = (a.sku || "").localeCompare(b.sku || "")
          break
        case "stock":
          const stockA = currentStocks[a.id || ''] || 0
          const stockB = currentStocks[b.id || ''] || 0
          comparison = stockA - stockB
          break
        default:
          comparison = (a.name || "").localeCompare(b.name || "")
      }

      return sortOrder === "asc" ? comparison : -comparison
    })

  // Pagination
  const pagination = usePagination<ProdukGudang>({
    data: filteredAndSortedProducts,
    itemsPerPage: 10
  })

  // Function to get stock status
  const getStockStatus = (productId: string | undefined, minStock: number) => {
    if (!productId) return { status: "unknown", label: "Unknown" }

    const currentStock = currentStocks[productId] || 0

    if (currentStock <= 0) {
      return { status: "out", label: "Out of Stock" }
    } else if (currentStock < minStock) {
      return { status: "low", label: "Low Stock" }
    } else {
      return { status: "ok", label: "In Stock" }
    }
  }

  return (
    <ProtectedRoute>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Products</h1>
            <p className="text-muted-foreground">Manage your product catalog</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" asChild>
              <Link href="/products/import">
                <Upload className="mr-2 h-4 w-4" />
                Import Excel
              </Link>
            </Button>
            <Button asChild>
              <Link href="/products/add">
                <Plus className="mr-2 h-4 w-4" />
                Add Product
              </Link>
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Products</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatNumber(products.length)}</div>
              <p className="text-xs text-muted-foreground">
                Across {categories.length} categories
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">In Stock</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {formatNumber(products.filter(p => {
                  const status = getStockStatus(p.id, p.min_stock)
                  return status.status === "ok"
                }).length)}
              </div>
              <p className="text-xs text-muted-foreground">Products available</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Low Stock</CardTitle>
              <TrendingDown className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {formatNumber(products.filter(p => {
                  const status = getStockStatus(p.id, p.min_stock)
                  return status.status === "low"
                }).length)}
              </div>
              <p className="text-xs text-muted-foreground">Need restocking</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Out of Stock</CardTitle>
              <TrendingDown className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {formatNumber(products.filter(p => {
                  const status = getStockStatus(p.id, p.min_stock)
                  return status.status === "out"
                }).length)}
              </div>
              <p className="text-xs text-muted-foreground">Urgent attention</p>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Product List
            </CardTitle>
            <CardDescription>
              Search, filter, and manage all products in your inventory
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Search and Filters */}
            <div className="space-y-4 mb-6">
              <div className="flex flex-col md:flex-row gap-4">
                {/* Search */}
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search products by name, SKU, or category..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>

                {/* Sort */}
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name">Name</SelectItem>
                    <SelectItem value="category">Category</SelectItem>
                    <SelectItem value="sku">SKU</SelectItem>
                    <SelectItem value="stock">Stock Level</SelectItem>
                  </SelectContent>
                </Select>

                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
                >
                  {sortOrder === "asc" ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />}
                </Button>
              </div>

              <div className="flex flex-col md:flex-row gap-4">
                {/* Category Filter */}
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className="w-[200px]">
                    <SelectValue placeholder="Filter by category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* Stock Status Filter */}
                <Select value={stockFilter} onValueChange={setStockFilter}>
                  <SelectTrigger className="w-[200px]">
                    <SelectValue placeholder="Filter by stock status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Stock Status</SelectItem>
                    <SelectItem value="ok">In Stock</SelectItem>
                    <SelectItem value="low">Low Stock</SelectItem>
                    <SelectItem value="out">Out of Stock</SelectItem>
                  </SelectContent>
                </Select>

                {/* Clear Filters */}
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchTerm("")
                    setCategoryFilter("all")
                    setStockFilter("all")
                    setSortBy("name")
                    setSortOrder("asc")
                  }}
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Clear Filters
                </Button>
              </div>
            </div>

            {/* Results Summary */}
            <div className="flex justify-between items-center mb-4">
              <p className="text-sm text-muted-foreground">
                Found {filteredAndSortedProducts.length} products
              </p>
            </div>
            {isLoading ? (
              <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                <span className="ml-2 text-muted-foreground">Loading products...</span>
              </div>
            ) : error ? (
              <div className="py-8 text-center">
                <p className="text-destructive">{error}</p>
                <Button variant="outline" className="mt-4" onClick={() => window.location.reload()}>
                  Try Again
                </Button>
              </div>
            ) : products.length === 0 ? (
              <div className="py-8 text-center">
                <p className="text-muted-foreground">No products found</p>
                <Button asChild className="mt-4">
                  <Link href="/products/add">Add Your First Product</Link>
                </Button>
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>SKU</TableHead>
                      <TableHead>Unit</TableHead>
                      <TableHead className="text-right">Min Stock</TableHead>
                      <TableHead className="text-right">Current Stock</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {pagination.paginatedData.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                          No products found matching your criteria
                        </TableCell>
                      </TableRow>
                    ) : (
                      pagination.paginatedData.map((product: ProdukGudang) => {
                        const stockStatus = getStockStatus(product.id, product.min_stock)
                        const currentStock = currentStocks[product.id || ''] || 0
                        return (
                          <TableRow
                            key={product.id}
                            className={
                              stockStatus.status === "out"
                                ? "bg-red-50 dark:bg-red-950/20 border-red-200 dark:border-red-800"
                                : stockStatus.status === "low"
                                ? "bg-amber-50 dark:bg-amber-950/20 border-amber-200 dark:border-amber-800"
                                : ""
                            }
                          >
                            <TableCell>
                              <div className="font-medium">{product.name}</div>
                              <div className="text-sm text-muted-foreground">{product.description}</div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline" className="text-xs">
                                {product.category}
                              </Badge>
                            </TableCell>
                            <TableCell className="font-mono text-sm">{product.sku}</TableCell>
                            <TableCell>{product.unit}</TableCell>
                            <TableCell className="text-right font-mono">
                              {formatNumber(product.min_stock)}
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex items-center justify-end gap-2">
                                <span className={`font-mono ${
                                  stockStatus.status === "out" ? "text-red-600 dark:text-red-400 font-bold" :
                                  stockStatus.status === "low" ? "text-orange-600 dark:text-orange-400" : "text-green-600 dark:text-green-400"
                                }`}>
                                  {formatNumber(currentStock)}
                                </span>
                                <Badge
                                  variant="outline"
                                  className={
                                    stockStatus.status === "out"
                                      ? "bg-red-50 dark:bg-red-950/50 text-red-700 dark:text-red-300 border-red-200 dark:border-red-800"
                                      : stockStatus.status === "low"
                                      ? "bg-amber-50 dark:bg-amber-950/50 text-amber-700 dark:text-amber-300 border-amber-200 dark:border-amber-800"
                                      : "bg-emerald-50 dark:bg-emerald-950/50 text-emerald-700 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800"
                                  }
                                >
                                  {stockStatus.label}
                                </Badge>
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end gap-2">
                                <Button variant="outline" size="icon" asChild>
                                  <Link href={`/products/${product.id}`}>
                                    <Pencil className="h-4 w-4" />
                                  </Link>
                                </Button>
                                <Button variant="outline" size="icon" className="text-destructive">
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        )
                      })
                    )}
                  </TableBody>
                </Table>
              </div>
            )}

            {/* Pagination Controls */}
            {!isLoading && pagination.totalItems > 0 && (
              <div className="mt-4">
                <PaginationControls
                  currentPage={pagination.currentPage}
                  totalPages={pagination.totalPages}
                  totalItems={pagination.totalItems}
                  itemsPerPage={pagination.itemsPerPage}
                  startIndex={pagination.startIndex}
                  endIndex={pagination.endIndex}
                  canGoNext={pagination.canGoNext}
                  canGoPrevious={pagination.canGoPrevious}
                  goToPage={pagination.goToPage}
                  goToNextPage={pagination.goToNextPage}
                  goToPreviousPage={pagination.goToPreviousPage}
                  setItemsPerPage={pagination.setItemsPerPage}
                />
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </ProtectedRoute>
  )
}

