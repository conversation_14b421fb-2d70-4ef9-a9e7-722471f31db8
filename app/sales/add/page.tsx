import { getKangIders, getProduk } from "@/lib/directus"
import { SalesPlanForm } from "../sales-plan-form"

export default async function AddSalesPlanPage() {
  const [kangIders, products] = await Promise.all([
    getKangIders(),
    getProduk()
  ])

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Create Sales Plan</h1>
        <p className="text-muted-foreground">Create a new sales distribution plan</p>
      </div>

      <SalesPlanForm
        kangIders={kangIders}
        products={products}
        mode="add"
      />
    </div>
  )
}
