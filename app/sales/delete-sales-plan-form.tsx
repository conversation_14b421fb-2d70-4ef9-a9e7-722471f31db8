"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { deleteSalesPlan } from "@/lib/directus"
import type { SalesPlanned } from "@/lib/directus"

interface DeleteSalesPlanFormProps {
  salesPlan: SalesPlanned
}

export function DeleteSalesPlanForm({ salesPlan }: DeleteSalesPlanFormProps) {
  const router = useRouter()
  const [isDeleting, setIsDeleting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleDelete = async () => {
    setIsDeleting(true)
    setError(null)

    try {
      await deleteSalesPlan(salesPlan.id)
      router.push("/sales")
      router.refresh()
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred while deleting the sales plan")
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-destructive/15 text-destructive p-3 rounded-md">
          {error}
        </div>
      )}

      <div className="space-y-4">
        <p className="text-lg">
          Are you sure you want to delete the sales plan for{" "}
          <span className="font-medium">{salesPlan.kangider?.nama}</span> on{" "}
          <span className="font-medium">{new Date(salesPlan.date).toLocaleDateString()}</span>?
        </p>
        <p className="text-muted-foreground">
          This action cannot be undone. All associated data will be permanently deleted.
        </p>
      </div>

      <div className="flex justify-end gap-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.push("/sales")}
          disabled={isDeleting}
        >
          Cancel
        </Button>
        <Button
          type="button"
          variant="destructive"
          onClick={handleDelete}
          disabled={isDeleting}
        >
          {isDeleting ? "Deleting..." : "Delete Sales Plan"}
        </Button>
      </div>
    </div>
  )
}
