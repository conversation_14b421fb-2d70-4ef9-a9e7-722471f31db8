import { getSalesPlans } from "@/lib/directus"
import { DeleteSalesPlanForm } from "../../delete-sales-plan-form"
import { notFound } from "next/navigation"

export default async function DeleteSalesPlanPage({
  params,
}: {
  params: { id: string }
}) {
  const salesPlans = await getSalesPlans({ id: { _eq: params.id } })
  const salesPlan = salesPlans[0]

  if (!salesPlan) {
    notFound()
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Delete Sales Plan</h1>
        <p className="text-muted-foreground">Confirm deletion of sales plan</p>
      </div>

      <DeleteSalesPlanForm salesPlan={salesPlan} />
    </div>
  )
}
