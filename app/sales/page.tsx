import { Suspense } from 'react';
import { getKangIders, getSalesPlans, getProduk } from '@/lib/directus';
import { format } from 'date-fns';
import { SalesPlanTable } from './sales-plan-table';
import { Button } from '@/components/ui/button';
import { Plus, Calendar, Truck } from 'lucide-react';
import Link from 'next/link';

export const dynamic = 'force-dynamic';

async function SalesPage() {
  const today = format(new Date(), 'yyyy-MM-dd');
  const [, salesPlans, products] = await Promise.all([
    getKangIders(),
    getSalesPlans({ date: { _eq: today } }),
    getProduk(),
  ]);

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Sales Plan</h1>
          <p className="text-muted-foreground">Plan and manage daily sales distribution</p>
        </div>
        <div className="flex gap-2">
          <Button asChild>
            <Link href="/sales/add">
              <Plus className="mr-2 h-4 w-4" />
              Create Sales Plan
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/sales/calendar">
              <Calendar className="mr-2 h-4 w-4" />
              Calendar View
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/production/kangider">
              <Truck className="mr-2 h-4 w-4" />
              Kang Ider Distribution
            </Link>
          </Button>
        </div>
      </div>

      <SalesPlanTable 
        salesPlans={salesPlans || []} 
        products={products || []}
      />
    </div>
  );
}

export default function Page() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SalesPage />
    </Suspense>
  );
}
