"use client"

import { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import { Edit, Trash2, Truck, CheckCircle, Clock, AlertCircle } from "lucide-react"
import Link from "next/link"
import type { SalesPlanned, Produk } from "@/lib/directus"

interface SalesPlanTableProps {
  salesPlans: SalesPlanned[]
  products: Produk[]
}

export function SalesPlanTable({ salesPlans, products }: SalesPlanTableProps) {
  const { toast } = useToast()
  const [loadingPlans, setLoadingPlans] = useState<Set<string>>(new Set())
  const [localSalesPlans, setLocalSalesPlans] = useState<SalesPlanned[]>(salesPlans)

  // Handle Load Ider action
  const handleLoadIder = async (planId: string) => {
    try {
      setLoadingPlans(prev => new Set(prev).add(planId))

      const token = localStorage.getItem('directus_token')
      if (!token) {
        throw new Error('Authentication token not found')
      }

      const response = await fetch('/api/production/kangider', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          sales_plan_id: planId,
          action: 'load_ider'
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to process load ider')
      }

      const result = await response.json()

      // Update local state
      setLocalSalesPlans(prev => prev.map(plan =>
        plan.id === planId ? { ...plan, status: "load ider" as const } : plan
      ))

      toast({
        title: "Success",
        description: result.data.message || "Load ider processed successfully",
      })
    } catch (error) {
      console.error("Error processing load ider:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to process load ider",
        variant: "destructive"
      })
    } finally {
      setLoadingPlans(prev => {
        const newSet = new Set(prev)
        newSet.delete(planId)
        return newSet
      })
    }
  }

  // Get status badge with enhanced styling
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "draft":
        return (
          <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
            <Clock className="h-3 w-3 mr-1" />
            Draft
          </Badge>
        )
      case "published":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <AlertCircle className="h-3 w-3 mr-1" />
            Published
          </Badge>
        )
      case "load ider":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            Load Ider
          </Badge>
        )
      default:
        return (
          <Badge variant="outline">
            {status}
          </Badge>
        )
    }
  }
  // Group sales plans by date
  const groupedByDate = localSalesPlans.reduce(
    (acc, plan) => {
      const date = plan.date
      if (!acc[date]) {
        acc[date] = []
      }
      acc[date].push(plan)
      return acc
    },
    {} as Record<string, SalesPlanned[]>,
  )

  // If no sales plans, show empty state
  if (localSalesPlans.length === 0) {
    return (
      <div className="text-center py-6">
        <p className="text-muted-foreground">No sales plans created for today.</p>
        <Button className="mt-4" asChild>
          <Link href="/sales/add">Create Sales Plan</Link>
        </Button>
      </div>
    )
  }

  // Get all unique product IDs from all sales plans
  const allProductIds = new Set<string>()
  localSalesPlans.forEach((plan) => {
    plan.items?.forEach((item) => {
      if (item.produk?.id) {
        allProductIds.add(item.produk.id)
      }
    })
  })

  // Create a mapping of product IDs to their column index
  const productColumns = Array.from(allProductIds).map((id) => {
    const product = products.find(p => p.id === id)
    return {
      id,
      name: product?.nama_produk || `Product ${id}`,
      shortName: product?.nama_produk?.substring(0, 3).toUpperCase() || `P${id.substring(0, 2)}`,
    }
  })

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[100px]">Date</TableHead>
            <TableHead className="w-[150px]">Kang Ider</TableHead>
            {productColumns.map((product) => (
              <TableHead key={product.id} title={product.name}>
                {product.shortName}
              </TableHead>
            ))}
            <TableHead>Total Cup</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {localSalesPlans.map((plan) => (
            <TableRow key={plan.id}>
              <TableCell>{new Date(plan.date).toLocaleDateString()}</TableCell>
              <TableCell className="font-medium">{plan.kangider?.nama || `Kang Ider ${plan.kangider}`}</TableCell>

              {/* Product quantities */}
              {productColumns.map((product) => {
                const item = plan.items?.find((item) => item.produk?.id === product.id)
                return <TableCell key={product.id}>{item?.quantity || 0}</TableCell>
              })}

              <TableCell>{plan.total_cup}</TableCell>
              <TableCell>
                {getStatusBadge(plan.status)}
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end gap-2">
                  {/* Edit button - only for draft status */}
                  {plan.status === "draft" && (
                    <Button variant="outline" size="icon" asChild>
                      <Link href={`/sales/edit/${plan.id}`}>
                        <Edit className="h-4 w-4" />
                      </Link>
                    </Button>
                  )}

                  {/* Load Ider button - only for published status */}
                  {plan.status === "published" && (
                    <Button
                      size="sm"
                      onClick={() => handleLoadIder(plan.id)}
                      disabled={loadingPlans.has(plan.id)}
                    >
                      {loadingPlans.has(plan.id) ? (
                        <>
                          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1"></div>
                          Loading...
                        </>
                      ) : (
                        <>
                          <Truck className="h-4 w-4 mr-1" />
                          Load Ider
                        </>
                      )}
                    </Button>
                  )}

                  {/* Delete button - only for draft status */}
                  {plan.status === "draft" && (
                    <Button variant="outline" size="icon" className="text-destructive">
                      <Link href={`/sales/delete/${plan.id}`}>
                        <Trash2 className="h-4 w-4" />
                      </Link>
                    </Button>
                  )}

                  {/* View button - for all statuses */}
                  <Button variant="outline" size="icon" asChild>
                    <Link href={`/sales/view/${plan.id}`}>
                      <CheckCircle className="h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}

          {/* Total row */}
          {localSalesPlans.length > 1 && (
            <TableRow className="font-bold">
              <TableCell colSpan={2}>TOTAL</TableCell>

              {/* Total for each product */}
              {productColumns.map((product) => {
                const total = localSalesPlans.reduce((sum, plan) => {
                  const item = plan.items?.find((item) => item.produk?.id === product.id)
                  return sum + (item?.quantity || 0)
                }, 0)

                return <TableCell key={product.id}>{total}</TableCell>
              })}

              {/* Total cups */}
              <TableCell>{localSalesPlans.reduce((sum, plan) => sum + (plan.total_cup || 0), 0)}</TableCell>

              <TableCell colSpan={2}></TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  )
}
