import { Suspense } from 'react'
import { notFound } from 'next/navigation'
import { getSalesPlans, getProduk, getKangIders } from '@/lib/directus'
import { format } from 'date-fns'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, Edit, CheckCircle, Clock, AlertCircle } from 'lucide-react'
import Link from 'next/link'

export const dynamic = 'force-dynamic'

interface PageProps {
  params: {
    id: string
  }
}

async function SalesPlanViewPage({ params }: PageProps) {
  const [salesPlans, products, kangIders] = await Promise.all([
    getSalesPlans({ id: { _eq: params.id } }),
    getProduk(),
    getKangIders(),
  ])

  const salesPlan = salesPlans?.[0]
  if (!salesPlan) {
    notFound()
  }

  const kangIder = kangIders.find(k => k.id === salesPlan.kangider)

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "draft":
        return (
          <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
            <Clock className="h-3 w-3 mr-1" />
            Draft
          </Badge>
        )
      case "published":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <AlertCircle className="h-3 w-3 mr-1" />
            Published
          </Badge>
        )
      case "load ider":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            Load Ider
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center space-x-4">
        <Button variant="outline" size="sm" asChild>
          <Link href="/sales">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Sales Plans
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Sales Plan Details</h1>
          <p className="text-muted-foreground">
            View sales plan information and items
          </p>
        </div>
      </div>

      {/* Sales Plan Info */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>Sales Plan Information</CardTitle>
              <CardDescription>
                Created on {format(new Date(salesPlan.date), "dd MMMM yyyy")}
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              {getStatusBadge(salesPlan.status)}
              {salesPlan.status === "draft" && (
                <Button size="sm" asChild>
                  <Link href={`/sales/edit/${salesPlan.id}`}>
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Link>
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Sales Plan ID</label>
              <p className="font-mono text-sm">{salesPlan.id}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Kang Ider</label>
              <p className="font-medium">{kangIder?.nama || 'Unknown'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Date</label>
              <p className="font-medium">{format(new Date(salesPlan.date), "dd MMM yyyy")}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Total Cups</label>
              <p className="text-2xl font-bold text-primary">{salesPlan.total_cup}</p>
            </div>
          </div>

          {kangIder && (
            <div className="mt-6 p-4 bg-muted rounded-lg">
              <h4 className="font-medium mb-2">Kang Ider Details</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Phone:</span>
                  <p>{kangIder.notelp || '-'}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">Bank:</span>
                  <p>{kangIder.nama_bank || '-'}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">Account:</span>
                  <p>{kangIder.norekening || '-'}</p>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Items Table */}
      <Card>
        <CardHeader>
          <CardTitle>Sales Plan Items</CardTitle>
          <CardDescription>
            Products and quantities in this sales plan
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!salesPlan.items || salesPlan.items.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No items in this sales plan</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Unit Price</TableHead>
                  <TableHead className="text-right">Total Value</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {salesPlan.items.map((item, index) => {
                  const product = products.find(p => p.id === item.produk)
                  const unitPrice = product?.harga || 0
                  const totalValue = item.quantity * unitPrice

                  return (
                    <TableRow key={index}>
                      <TableCell className="font-medium">
                        {product?.nama_produk || `Product ${item.produk}`}
                      </TableCell>
                      <TableCell>{item.quantity}</TableCell>
                      <TableCell>
                        {unitPrice > 0 ? `Rp ${unitPrice.toLocaleString()}` : '-'}
                      </TableCell>
                      <TableCell className="text-right font-medium">
                        {totalValue > 0 ? `Rp ${totalValue.toLocaleString()}` : '-'}
                      </TableCell>
                    </TableRow>
                  )
                })}
                
                {/* Total Row */}
                <TableRow className="font-bold border-t-2">
                  <TableCell>TOTAL</TableCell>
                  <TableCell>{salesPlan.total_cup}</TableCell>
                  <TableCell>-</TableCell>
                  <TableCell className="text-right">
                    Rp {salesPlan.items.reduce((sum, item) => {
                      const product = products.find(p => p.id === item.produk)
                      const unitPrice = product?.harga || 0
                      return sum + (item.quantity * unitPrice)
                    }, 0).toLocaleString()}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Status Information */}
      <Card>
        <CardHeader>
          <CardTitle>Status Information</CardTitle>
          <CardDescription>
            Current status and workflow information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                {getStatusBadge(salesPlan.status)}
              </div>
              <div>
                <p className="font-medium">
                  {salesPlan.status === "draft" && "Draft - Ready for review and publishing"}
                  {salesPlan.status === "published" && "Published - Ready for Kang Ider pickup"}
                  {salesPlan.status === "load ider" && "Load Ider - Picked up by Kang Ider"}
                </p>
                <p className="text-sm text-muted-foreground">
                  {salesPlan.status === "draft" && "You can still edit this sales plan"}
                  {salesPlan.status === "published" && "Waiting for gudang team to process"}
                  {salesPlan.status === "load ider" && "Stock has been distributed and kas_harian created"}
                </p>
              </div>
            </div>

            {salesPlan.status === "published" && (
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-sm text-blue-800">
                  <strong>Next Step:</strong> Gudang team will process this sales plan and mark it as "Load Ider" 
                  when the products are picked up by the Kang Ider.
                </p>
              </div>
            )}

            {salesPlan.status === "load ider" && (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-sm text-green-800">
                  <strong>Completed:</strong> This sales plan has been processed. Stock has been distributed 
                  and kas_harian record has been created for tracking.
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default function Page({ params }: PageProps) {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SalesPlanViewPage params={params} />
    </Suspense>
  )
}
