"use client"

import { useEffect, useState } from "react"
import { useSearchParams } from "next/navigation"
import { useToast } from "@/components/ui/use-toast"
import { getProducts } from "@/lib/directus"
import { StockMovementForm } from "./stock-movement-form"
import { ProtectedRoute } from '@/components/auth/protected-route'
import type { ProdukGudang } from "@/lib/directus"

export default function AddStockMovementPage() {
  const searchParams = useSearchParams()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)
  const [products, setProducts] = useState<ProdukGudang[]>([])

  // Fetch products from Directus
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const productsData = await getProducts()
        setProducts(productsData)
      } catch (error) {
        console.error("Error fetching products:", error)
        toast({
          title: "Error",
          description: "Failed to load products. Please try again.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchProducts()
  }, [toast])

  const initialProductId = searchParams.get("product") || undefined
  const initialType = searchParams.get("type") as "incoming" | "outgoing" | undefined

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-lg font-semibold">Loading products...</h2>
          <p className="text-muted-foreground">Please wait</p>
        </div>
      </div>
    )
  }

  return (
    <ProtectedRoute>
      <StockMovementForm
        products={products}
        initialProductId={initialProductId}
        initialType={initialType}
      />
    </ProtectedRoute>
  )
}

