"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "@/hooks/use-toast"
import { ArrowDownCircle, ArrowUpCircle } from "lucide-react"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import type { ProdukGudang, Vendor } from "@/lib/directus"
import { addStockMovementWithConversion, fetchVendors, fetchAvailableUnitsForProduct } from "@/app/actions"

const formSchema = z.object({
  produk_gudang_id: z.string({
    required_error: "Please select a product",
  }),
  type: z.enum(["incoming", "outgoing"], {
    required_error: "Please select a movement type",
  }),
  quantity: z.coerce.number().positive({
    message: "Quantity must be a positive number",
  }),
  unit_used: z.string().optional(), // ID of conversion unit, "base" for base unit
  vendor_id: z.string().optional(), // "none" for no vendor
  purchase_price: z.union([
    z.string().length(0),
    z.string().transform((val) => parseFloat(val)).pipe(z.number().positive())
  ]).optional(), // harga pembelian per unit
  date: z.string().min(1, {
    message: "Please select a date",
  }),
  reference_number: z.string().optional(),
  notes: z.string().optional(),
})

interface StockMovementFormProps {
  products: ProdukGudang[]
  initialProductId?: string
  initialType?: "incoming" | "outgoing"
}

export function StockMovementForm({ products, initialProductId, initialType }: StockMovementFormProps) {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [authError, setAuthError] = useState(false)
  const [vendors, setVendors] = useState<Vendor[]>([])
  const [availableUnits, setAvailableUnits] = useState<any[]>([])
  const [isLoadingUnits, setIsLoadingUnits] = useState(false)

  // Check for authentication token on component mount
  useEffect(() => {
    const token = typeof window !== 'undefined' ? localStorage.getItem('directus_token') : null
    if (!token) {
      setAuthError(true)
      toast({
        title: "Authentication Error",
        description: "You need to be logged in to perform this action. Please log in again.",
        variant: "destructive",
      })
      router.push('/login')
    }
  }, [router])

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      produk_gudang_id: initialProductId || "",
      type: initialType || "incoming",
      quantity: 1,
      unit_used: "base", // "base" for base unit
      vendor_id: "none",
      purchase_price: "",
      date: new Date().toISOString().split("T")[0],
      reference_number: "",
      notes: "",
    },
  })

  const selectedProductId = form.watch("produk_gudang_id")
  const selectedProduct = products.find((p) => p.id === selectedProductId)
  const selectedUnitId = form.watch("unit_used")

  // Load vendors on component mount
  useEffect(() => {
    async function loadVendors() {
      try {
        const result = await fetchVendors()
        if (result.success && result.data) {
          setVendors(result.data)
        }
      } catch (error) {
        console.error("Error loading vendors:", error)
      }
    }
    loadVendors()
  }, [])

  // Load available units when product changes
  useEffect(() => {
    async function loadUnits() {
      if (!selectedProductId) {
        setAvailableUnits([])
        return
      }

      setIsLoadingUnits(true)
      try {
        const result = await fetchAvailableUnitsForProduct(selectedProductId)
        if (result.success && result.data) {
          setAvailableUnits(result.data)
          // Reset unit selection when product changes
          form.setValue("unit_used", "base")

          // Set default purchase price from product
          const selectedProduct = products.find(p => p.id === selectedProductId)
          if (selectedProduct?.purchase_price && !form.getValues("purchase_price")) {
            form.setValue("purchase_price", selectedProduct.purchase_price.toString())
          }
        }
      } catch (error) {
        console.error("Error loading units:", error)
        setAvailableUnits([])
      } finally {
        setIsLoadingUnits(false)
      }
    }
    loadUnits()
  }, [selectedProductId, form, products])

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsSubmitting(true)

    // Add debug logging
    console.log("Submitting form with values:", values)

    // Check for authentication token
    const token = typeof window !== 'undefined' ? localStorage.getItem('directus_token') : null
    console.log("Token exists:", !!token)

    try {
      // Check for authentication token before submission
      if (!token) {
        throw new Error("Authentication token not found. Please log in again.")
      }

      // Convert purchase_price to number if it's a string
      const processedValues = {
        ...values,
        purchase_price: typeof values.purchase_price === 'string' && values.purchase_price !== ''
          ? parseFloat(values.purchase_price)
          : typeof values.purchase_price === 'number'
            ? values.purchase_price
            : undefined
      }

      // Use the enhanced stock movement function with conversion
      const result = await addStockMovementWithConversion(processedValues)

      console.log("API response:", result)

      if (result.success) {
        // Get the unit name for display
        const selectedUnit = availableUnits.find(u => u.id === values.unit_used)
        const unitName = selectedUnit ? selectedUnit.unit_name : selectedProduct?.unit || 'units'

        toast({
          title: "Stock movement recorded",
          description: `${values.type === "incoming" ? "Incoming" : "Outgoing"} movement of ${values.quantity} ${unitName} has been recorded.`,
        })

        router.push("/stock")
      } else {
        // Check if it's an authentication error from the API
        if (result.authError) {
          setAuthError(true)
          toast({
            title: "Authentication Error",
            description: result.error,
            variant: "destructive",
          })
          router.push('/login')
        } else {
          throw new Error(result.error)
        }
      }
    } catch (error) {
      // Enhanced error logging
      console.error("Error submitting form:", error)

      // Check if it's an authentication error
      const errorMessage = error instanceof Error ? error.message : "Failed to record stock movement. Please try again."
      if (errorMessage.includes("Authentication") || errorMessage.includes("token")) {
        setAuthError(true)
        router.push('/login')
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // If authentication error is detected, don't render the form
  if (authError) {
    return null
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Record Stock Movement</h1>
        <p className="text-muted-foreground">Record incoming or outgoing stock movements</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Movement Details</CardTitle>
          <CardDescription>Enter the details of the stock movement</CardDescription>
        </CardHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel>Movement Type</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex flex-col space-y-1"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="incoming" id="incoming" />
                          <label htmlFor="incoming" className="flex items-center gap-1 cursor-pointer">
                            <ArrowDownCircle className="h-4 w-4 text-green-500" />
                            <span>Incoming (Stock In)</span>
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="outgoing" id="outgoing" />
                          <label htmlFor="outgoing" className="flex items-center gap-1 cursor-pointer">
                            <ArrowUpCircle className="h-4 w-4 text-red-500" />
                            <span>Outgoing (Stock Out)</span>
                          </label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="produk_gudang_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Product</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a product" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {products.filter(product => product.id).map((product) => (
                            <SelectItem key={product.id} value={product.id!}>
                              {product.name} ({product.sku})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Unit Selection and Vendor */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="unit_used"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Unit</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        disabled={!selectedProductId || isLoadingUnits}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={
                              isLoadingUnits ? "Loading units..." :
                              !selectedProductId ? "Select product first" :
                              "Select unit"
                            } />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {availableUnits.map((unit) => (
                            <SelectItem key={unit.id || 'base'} value={unit.id || "base"}>
                              {unit.unit_name} {unit.is_base ? "(Base Unit)" : `(1 = ${unit.conversion_to_base} ${selectedProduct?.unit})`}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="vendor_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Vendor (Optional)</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select vendor" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">No Vendor</SelectItem>
                          {vendors.map((vendor) => (
                            <SelectItem key={vendor.id} value={vendor.id}>
                              {vendor.vendor_name} ({vendor.vendor_code})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="quantity"
                  render={({ field }) => {
                    // Get the selected unit for display
                    const selectedUnit = availableUnits.find(u => u.id === selectedUnitId)
                    const displayUnit = selectedUnit ? selectedUnit.unit_name : selectedProduct?.unit || 'units'

                    return (
                      <FormItem>
                        <FormLabel>Quantity ({displayUnit})</FormLabel>
                        <div className="flex items-center gap-2">
                          <FormControl>
                            <Input type="number" step="0.01" {...field} />
                          </FormControl>
                          <span className="text-sm text-muted-foreground">{displayUnit}</span>
                        </div>
                        {selectedUnit && !selectedUnit.is_base && selectedUnitId !== "base" && (
                          <p className="text-xs text-muted-foreground">
                            Will be converted to {(parseFloat(String(field.value || "0")) * selectedUnit.conversion_to_base).toFixed(2)} {selectedProduct?.unit}
                          </p>
                        )}
                        <FormMessage />
                      </FormItem>
                    )
                  }}
                />

                <FormField
                  control={form.control}
                  name="purchase_price"
                  render={({ field }) => {
                    // Get the selected unit for display
                    const selectedUnit = availableUnits.find(u => u.id === selectedUnitId)
                    const displayUnit = selectedUnit ? selectedUnit.unit_name : selectedProduct?.unit || 'units'

                    return (
                      <FormItem>
                        <FormLabel>Purchase Price per {displayUnit} (Optional)</FormLabel>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-muted-foreground">Rp</span>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="0.00"
                              {...field}
                            />
                          </FormControl>
                        </div>
                        {field.value && field.value !== "" && (
                          <p className="text-xs text-muted-foreground">
                            Total cost: Rp {(parseFloat(String(field.value || "0")) * parseFloat(String(form.watch("quantity") || "0"))).toLocaleString('id-ID', { minimumFractionDigits: 2 })}
                          </p>
                        )}
                        <FormMessage />
                      </FormItem>
                    )
                  }}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="reference_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reference Number (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Invoice or PO number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes (Optional)</FormLabel>
                    <FormControl>
                      <Textarea placeholder="Additional information about this movement" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" type="button" onClick={() => router.back()}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Saving..." : "Record Movement"}
              </Button>
            </CardFooter>
          </form>
        </Form>
      </Card>
    </div>
  )
}

