"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { ArrowLeft, Plus, Trash2, Package, ShoppingCart, History } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import type { ProdukGudang, Vendor } from "@/lib/directus"
import { fetchAvailableUnitsForProduct } from "@/app/actions"

// Extended unit type that includes base unit and conversion units
interface AvailableUnit {
  id: string
  unit_name: string
  conversion_to_base: number
  is_base: boolean
  id_produk_gudang?: string
}

interface BulkMovementItem {
  id: string
  productId: string
  quantity: number
  unitUsed: string
  purchasePrice: number
  notes: string
}

interface BulkStockMovementFormProps {
  products: ProdukGudang[]
  vendors: Vendor[]
}

export function BulkStockMovementForm({ products, vendors }: BulkStockMovementFormProps) {
  const router = useRouter()
  const { toast } = useToast()

  // Form state
  const [type, setType] = useState<"incoming" | "outgoing">("incoming")
  const [date, setDate] = useState(new Date().toISOString().split('T')[0])
  const [vendorId, setVendorId] = useState<string>("")
  const [referenceNumber, setReferenceNumber] = useState("")
  const [title, setTitle] = useState("")
  const [generalNotes, setGeneralNotes] = useState("")
  const [items, setItems] = useState<BulkMovementItem[]>([
    {
      id: crypto.randomUUID(),
      productId: "",
      quantity: 0,
      unitUsed: "base",
      purchasePrice: 0,
      notes: ""
    }
  ])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitAttempts, setSubmitAttempts] = useState(0)
  const submitTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Conversion units state - using AvailableUnit type
  const [availableUnits, setAvailableUnits] = useState<Record<string, AvailableUnit[]>>({})
  const [loadingUnits, setLoadingUnits] = useState<Record<string, boolean>>({})

  // Refs for focus management
  const productSelectRefs = useRef<Record<string, HTMLButtonElement | null>>({})
  const quantityInputRefs = useRef<Record<string, HTMLInputElement | null>>({})

  // Add new item
  const addItem = useCallback(() => {
    const newItemId = crypto.randomUUID()
    setItems(prev => [...prev, {
      id: newItemId,
      productId: "",
      quantity: 0,
      unitUsed: "base",
      purchasePrice: 0,
      notes: ""
    }])

    // Focus on the product select of the new item after a short delay
    setTimeout(() => {
      const productSelect = productSelectRefs.current[newItemId]
      if (productSelect) {
        productSelect.click()
      }
    }, 100)
  }, [items])

  // Remove item
  const removeItem = (id: string) => {
    if (items.length > 1) {
      setItems(items.filter(item => item.id !== id))
    }
  }

  // Update item
  const updateItem = (id: string, field: keyof BulkMovementItem, value: any) => {
    setItems(items.map(item =>
      item.id === id ? { ...item, [field]: value } : item
    ))

    // If product is changed, load conversion units and reset unit
    if (field === 'productId' && value) {
      loadConversionUnits(value)
      setItems(items.map(item =>
        item.id === id ? { ...item, productId: value, unitUsed: "base" } : item
      ))

      // Set default purchase price from product
      const selectedProduct = products.find(p => p.id === value)
      if (selectedProduct?.purchase_price) {
        setItems(items.map(item =>
          item.id === id ? { ...item, productId: value, unitUsed: "base", purchasePrice: selectedProduct.purchase_price || 0 } : item
        ))
      }

      // Focus on quantity input after product selection
      focusQuantityInput(id)
    }
  }

  // Load conversion units for a product
  const loadConversionUnits = async (productId: string) => {
    if (availableUnits[productId] || loadingUnits[productId]) return

    setLoadingUnits(prev => ({ ...prev, [productId]: true }))

    try {
      const result = await fetchAvailableUnitsForProduct(productId)
      if (result.success && result.data) {
        setAvailableUnits(prev => ({ ...prev, [productId]: result.data }))
      }
    } catch (error) {
      console.error("Error loading conversion units:", error)
    } finally {
      setLoadingUnits(prev => ({ ...prev, [productId]: false }))
    }
  }

  // Get product by ID
  const getProduct = (productId: string) => {
    return products.find(p => p.id === productId)
  }

  // Get available units for a product
  const getAvailableUnits = (productId: string) => {
    return availableUnits[productId] || []
  }

  // Focus on quantity input after product selection
  const focusQuantityInput = useCallback((itemId: string) => {
    setTimeout(() => {
      const quantityInput = quantityInputRefs.current[itemId]
      if (quantityInput) {
        quantityInput.focus()
        quantityInput.select()
      }
    }, 100)
  }, [])

  // Platform detection for keyboard shortcuts
  const [isMac, setIsMac] = useState(false)
  const modifierKey = isMac ? 'Cmd' : 'Ctrl'

  useEffect(() => {
    // Detect platform on client side
    setIsMac(navigator.userAgent.indexOf('Mac') !== -1)
  }, [])

  // Enhanced keyboard shortcuts with better Windows support
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Prevent shortcuts when user is typing in input fields
      const target = e.target as HTMLElement
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.tagName === 'SELECT') {
        // Only allow Ctrl+Enter for form submission when in input fields
        if ((e.ctrlKey && e.key === 'Enter') || (e.metaKey && e.key === 'Enter')) {
          e.preventDefault()
          console.log('🚀 Submitting form via keyboard shortcut from input field')
          handleSubmitViaKeyboard()
        }
        return
      }

      // Debug logging for troubleshooting Windows shortcuts
      if ((e.ctrlKey && e.key === 'Enter') ||
          (e.metaKey && e.key === 'Enter') ||
          (e.altKey && e.key.toLowerCase() === 'a') ||
          (e.ctrlKey && e.shiftKey && e.key.toLowerCase() === 'a')) {
        console.log('🎹 Keyboard shortcut detected:', {
          key: e.key,
          code: e.code,
          ctrlKey: e.ctrlKey,
          metaKey: e.metaKey,
          shiftKey: e.shiftKey,
          altKey: e.altKey,
          platform: isMac ? 'Mac' : 'Windows/Linux',
          userAgent: navigator.userAgent,
          target: target.tagName
        })
      }

      // Ctrl/Cmd + Enter to submit form - Enhanced for Windows
      if ((e.ctrlKey && e.key === 'Enter') || (e.metaKey && e.key === 'Enter')) {
        e.preventDefault()
        e.stopPropagation()
        console.log('🚀 Submitting form via keyboard shortcut')
        handleSubmitViaKeyboard()
        return
      }

      // Alt + A to add new item - Enhanced for Windows
      if (e.altKey && e.key.toLowerCase() === 'a' && !e.ctrlKey && !e.metaKey && !e.shiftKey) {
        e.preventDefault()
        e.stopPropagation()
        console.log('➕ Adding new item via Alt+A shortcut')
        addItem()
        return
      }

      // Backup: Ctrl/Cmd + Shift + A to add new item - Enhanced for Windows
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key.toLowerCase() === 'a') {
        e.preventDefault()
        e.stopPropagation()
        console.log('➕ Adding new item via Ctrl+Shift+A shortcut')
        addItem()
        return
      }
    }

    // Use capture phase to ensure we catch the event before other handlers
    document.addEventListener('keydown', handleKeyDown, true)
    return () => document.removeEventListener('keydown', handleKeyDown, true)
  }, [addItem, isMac])

  // Cleanup effect for timeout
  useEffect(() => {
    return () => {
      if (submitTimeoutRef.current) {
        clearTimeout(submitTimeoutRef.current)
      }
    }
  }, [])

  // Separate function to handle keyboard-triggered submissions
  const handleSubmitViaKeyboard = useCallback(() => {
    if (isSubmitting) {
      console.log('⚠️ Form is already submitting, ignoring keyboard shortcut')
      return
    }

    const form = document.querySelector('form')
    if (form) {
      // Create a synthetic submit event
      const submitEvent = new Event('submit', { bubbles: true, cancelable: true })
      form.dispatchEvent(submitEvent)
    }
  }, [isSubmitting])

  // Handle form submission with enhanced double-submit prevention
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Enhanced double-submit prevention
    if (isSubmitting) {
      console.log('⚠️ Form is already submitting, preventing double submit')
      toast({
        title: "Please wait",
        description: "Form is already being submitted...",
        variant: "default"
      })
      return
    }

    // Clear any existing timeout
    if (submitTimeoutRef.current) {
      clearTimeout(submitTimeoutRef.current)
    }

    // Track submit attempts for debugging
    setSubmitAttempts(prev => prev + 1)
    console.log(`📝 Form submission attempt #${submitAttempts + 1}`)

    // Validation
    if (!date) {
      toast({
        title: "Validation Error",
        description: "Please select a date",
        variant: "destructive"
      })
      return
    }

    if (!title.trim()) {
      toast({
        title: "Validation Error",
        description: "Please enter a title for this bulk movement",
        variant: "destructive"
      })
      return
    }

    if (type === "incoming" && !vendorId) {
      toast({
        title: "Validation Error",
        description: "Please select a vendor for incoming stock",
        variant: "destructive"
      })
      return
    }

    // Validate items
    const validItems = items.filter(item => item.productId && item.quantity > 0)
    if (validItems.length === 0) {
      toast({
        title: "Validation Error",
        description: "Please add at least one valid item",
        variant: "destructive"
      })
      return
    }

    // Set submitting state immediately to prevent double clicks
    setIsSubmitting(true)

    // Set a timeout to automatically reset submitting state in case of network issues
    submitTimeoutRef.current = setTimeout(() => {
      console.log('⏰ Submit timeout reached, resetting submitting state')
      setIsSubmitting(false)
    }, 30000) // 30 seconds timeout
    setSubmitAttempts(prev => prev + 1)

    try {
      console.log('🚀 Starting bulk movement submission...')

      // Get token from localStorage
      const token = localStorage.getItem('directus_token')
      if (!token) {
        throw new Error('Authentication token not found. Please login again.')
      }

      const requestBody = {
        type,
        date,
        vendorId: type === "incoming" ? vendorId : undefined,
        referenceNumber,
        title,
        generalNotes,
        items: validItems
      }

      console.log('📤 Sending request with data:', {
        ...requestBody,
        itemCount: validItems.length
      })

      const response = await fetch('/api/stock/bulk-movement', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestBody)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create bulk stock movement')
      }

      const result = await response.json()
      console.log('✅ Bulk movement created successfully:', result)

      toast({
        title: "Success",
        description: `Bulk stock movement created successfully. ${result.success} out of ${result.total} items processed.`,
      })

      // Clear the timeout since submission was successful
      if (submitTimeoutRef.current) {
        clearTimeout(submitTimeoutRef.current)
        submitTimeoutRef.current = null
      }

      router.push('/stock')

    } catch (error) {
      console.error('❌ Error creating bulk movement:', error)

      // Clear the timeout on error
      if (submitTimeoutRef.current) {
        clearTimeout(submitTimeoutRef.current)
        submitTimeoutRef.current = null
      }

      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create bulk stock movement",
        variant: "destructive"
      })
    } finally {
      // Always reset submitting state
      setIsSubmitting(false)
      console.log('🏁 Form submission completed, resetting state')
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Bulk Stock Movement</h1>
            <p className="text-muted-foreground">
              Create stock movement for multiple products at once
            </p>
          </div>
        </div>
        <Button
          variant="outline"
          onClick={() => router.push('/stock/bulk-movement/history')}
        >
          <History className="h-4 w-4 mr-2" />
          View History
        </Button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* General Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              {type === "incoming" ? (
                <Package className="h-5 w-5 text-green-600" />
              ) : (
                <ShoppingCart className="h-5 w-5 text-blue-600" />
              )}
              <span>General Information</span>
            </CardTitle>
            <CardDescription>
              Basic information for this bulk stock movement
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="type">Movement Type</Label>
                <Select value={type} onValueChange={(value: "incoming" | "outgoing") => setType(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="incoming">Incoming (Stock In)</SelectItem>
                    <SelectItem value="outgoing">Outgoing (Stock Out)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="date">Date</Label>
                <Input
                  id="date"
                  type="date"
                  value={date}
                  onChange={(e) => setDate(e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                placeholder={type === "incoming" ? "e.g., Purchase from Vendor ABC" : "e.g., Production for Menu XYZ"}
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                required
              />
            </div>

            {type === "incoming" && (
              <div className="space-y-2">
                <Label htmlFor="vendor">Vendor *</Label>
                <Select value={vendorId} onValueChange={setVendorId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select vendor" />
                  </SelectTrigger>
                  <SelectContent>
                    {vendors.map((vendor) => (
                      <SelectItem key={vendor.id} value={vendor.id.toString()}>
                        {vendor.vendor_name} ({vendor.vendor_code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="referenceNumber">Reference Number</Label>
              <Input
                id="referenceNumber"
                placeholder="e.g., PO-2024-001 or PROD-2024-001"
                value={referenceNumber}
                onChange={(e) => setReferenceNumber(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="generalNotes">General Notes</Label>
              <Textarea
                id="generalNotes"
                placeholder="Additional notes for this bulk movement..."
                value={generalNotes}
                onChange={(e) => setGeneralNotes(e.target.value)}
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Items */}
        <Card>
          <CardHeader>
            <div>
              <CardTitle>Items</CardTitle>
              <CardDescription>
                Add products and quantities for this bulk movement
              </CardDescription>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {items.map((item, index) => (
              <div
                key={item.id}
                className="border rounded-lg p-4 space-y-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                tabIndex={0}
                onKeyDown={(e) => {
                  // Enhanced delete shortcut for Windows compatibility
                  if ((e.key === 'Delete' || e.key === 'Backspace') && (e.ctrlKey || e.metaKey)) {
                    console.log('🗑️ Delete shortcut detected:', {
                      key: e.key,
                      ctrlKey: e.ctrlKey,
                      metaKey: e.metaKey,
                      itemsLength: items.length,
                      canDelete: items.length > 1,
                      platform: isMac ? 'Mac' : 'Windows/Linux'
                    })
                  }

                  // Support both Delete and Backspace keys for better Windows compatibility
                  if ((e.key === 'Delete' || e.key === 'Backspace') && (e.ctrlKey || e.metaKey) && items.length > 1) {
                    e.preventDefault()
                    e.stopPropagation()
                    console.log('🗑️ Removing item via keyboard shortcut:', item.id)
                    removeItem(item.id)
                  }
                }}
              >
                <div className="flex items-center justify-between">
                  <Badge variant="outline">Item #{index + 1}</Badge>
                  {items.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeItem(item.id)}
                      className="text-destructive"
                      title={`Delete item (${modifierKey}+Delete or ${modifierKey}+Backspace)`}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>Product *</Label>
                    <Select
                      value={item.productId}
                      onValueChange={(value) => updateItem(item.id, 'productId', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select product" />
                      </SelectTrigger>
                      <SelectContent>
                        {products.map((product) => (
                          <SelectItem key={product.id} value={product.id!}>
                            {product.name} ({product.sku})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Quantity *</Label>
                    <div className="space-y-2">
                      <Input
                        ref={(el) => {
                          quantityInputRefs.current[item.id] = el
                        }}
                        type="number"
                        min="0"
                        step="0.01"
                        value={item.quantity}
                        onChange={(e) => updateItem(item.id, 'quantity', parseFloat(e.target.value) || 0)}
                        placeholder="0"
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault()
                            // Focus next input or add new item if this is the last one
                            const currentIndex = items.findIndex(i => i.id === item.id)
                            if (currentIndex === items.length - 1) {
                              addItem()
                            } else {
                              const nextItem = items[currentIndex + 1]
                              if (nextItem) {
                                const nextQuantityInput = quantityInputRefs.current[nextItem.id]
                                if (nextQuantityInput) {
                                  nextQuantityInput.focus()
                                  nextQuantityInput.select()
                                }
                              }
                            }
                          }
                        }}
                      />
                      {item.productId && item.unitUsed !== "base" && (() => {
                        const selectedUnit = getAvailableUnits(item.productId).find(u => u.id === item.unitUsed)
                        const product = getProduct(item.productId)
                        if (selectedUnit && !selectedUnit.is_base && product) {
                          const convertedQuantity = (item.quantity * selectedUnit.conversion_to_base).toFixed(2)
                          return (
                            <p className="text-xs text-muted-foreground">
                              = {convertedQuantity} {product.unit} (base unit)
                            </p>
                          )
                        }
                        return null
                      })()}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Unit</Label>
                    <Select
                      value={item.unitUsed}
                      onValueChange={(value) => updateItem(item.id, 'unitUsed', value)}
                      disabled={!item.productId || loadingUnits[item.productId]}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={
                          loadingUnits[item.productId] ? "Loading units..." :
                          !item.productId ? "Select product first" :
                          "Select unit"
                        } />
                      </SelectTrigger>
                      <SelectContent>
                        {getAvailableUnits(item.productId).map((unit) => (
                          <SelectItem key={unit.id || 'base'} value={unit.id || "base"}>
                            {unit.unit_name} {unit.is_base ? "(Base Unit)" : `(1 = ${unit.conversion_to_base} ${getProduct(item.productId)?.unit})`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Purchase Price</Label>
                    <Input
                      type="number"
                      min="0"
                      step="0.01"
                      value={item.purchasePrice}
                      onChange={(e) => updateItem(item.id, 'purchasePrice', parseFloat(e.target.value) || 0)}
                      placeholder="0"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Item Notes</Label>
                    <Input
                      value={item.notes}
                      onChange={(e) => updateItem(item.id, 'notes', e.target.value)}
                      placeholder="Notes for this item..."
                    />
                  </div>
                </div>
              </div>
            ))}

            {/* Add Item Button */}
            <div className="flex justify-center pt-4">
              <Button
                type="button"
                onClick={addItem}
                variant="outline"
                size="sm"
                className="w-full max-w-xs"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Item (Alt+A)
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Submit */}
        <div className="flex justify-between items-center">
          <div className="text-sm text-muted-foreground">
            <p>Keyboard shortcuts:</p>
            <ul className="list-disc list-inside space-y-1 mt-1">
              <li><kbd className="px-1 py-0.5 bg-muted rounded text-xs">{modifierKey}+Enter</kbd> Submit form</li>
              <li><kbd className="px-1 py-0.5 bg-muted rounded text-xs">Alt+A</kbd> Add new item</li>
              <li><kbd className="px-1 py-0.5 bg-muted rounded text-xs">{modifierKey}+Shift+A</kbd> Add new item (backup)</li>
              <li><kbd className="px-1 py-0.5 bg-muted rounded text-xs">Enter</kbd> Next field in quantity input</li>
              <li><kbd className="px-1 py-0.5 bg-muted rounded text-xs">{modifierKey}+Delete</kbd> Delete item</li>
              <li><kbd className="px-1 py-0.5 bg-muted rounded text-xs">{modifierKey}+Backspace</kbd> Delete item (backup)</li>
            </ul>
            {!isMac && (
              <p className="text-xs text-amber-600 mt-2">
                💡 Windows users: If shortcuts don't work, try clicking outside input fields first
              </p>
            )}
          </div>
          <div className="flex space-x-4">
            <Button type="button" variant="outline" onClick={() => router.back()} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              title={`Submit form (${modifierKey}+Enter)`}
              className={isSubmitting ? "opacity-50 cursor-not-allowed" : ""}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                "Create Bulk Movement"
              )}
            </Button>
          </div>
        </div>
      </form>
    </div>
  )
}
