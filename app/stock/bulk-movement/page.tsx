"use client"

import { useEffect, useState } from "react"
import { useToast } from "@/components/ui/use-toast"
import { getProducts, getVendors } from "@/lib/directus"
import { BulkStockMovementForm } from "./bulk-movement-form"
import { ProtectedRoute } from '@/components/auth/protected-route'
import type { ProdukGudang, Vendor } from "@/lib/directus"

export default function BulkStockMovementPage() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)
  const [products, setProducts] = useState<ProdukGudang[]>([])
  const [vendors, setVendors] = useState<Vendor[]>([])

  // Fetch products and vendors from Directus
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [productsData, vendorsData] = await Promise.all([
          getProducts(),
          getVendors()
        ])
        setProducts(productsData)
        setVendors(vendorsData)
      } catch (error) {
        console.error("Error fetching data:", error)
        toast({
          title: "Error",
          description: "Failed to load data. Please try again.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [toast])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-lg font-semibold">Loading data...</h2>
          <p className="text-muted-foreground">Please wait</p>
        </div>
      </div>
    )
  }

  return (
    <ProtectedRoute>
      <BulkStockMovementForm
        products={products}
        vendors={vendors}
      />
    </ProtectedRoute>
  )
}
