"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { ArrowDownCircle, ArrowUpCircle, Plus, FileText, Package2, Search, Filter, TrendingUp, TrendingDown, AlertTriangle, Package, History } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { getProducts, getCurrentStock } from "@/lib/directus"
import { ProtectedRoute } from '@/components/auth/protected-route'
import { usePagination } from "@/hooks/use-pagination"
import { PaginationControls } from "@/components/ui/pagination-controls"
import type { ProdukGudang, CurrentStock } from "@/lib/directus"

export default function StockPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [products, setProducts] = useState<ProdukGudang[]>([])
  const [currentStocks, setCurrentStocks] = useState<Record<string, number>>({})

  // Search and filter states
  const [searchTerm, setSearchTerm] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [stockFilter, setStockFilter] = useState("all")
  const [sortBy, setSortBy] = useState("name")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc")
  const [categories, setCategories] = useState<string[]>([])

  useEffect(() => {
    async function fetchData() {
      try {
        const productsData = await getProducts()
        setProducts(productsData)

        const stockData = await getCurrentStock()
        const stocks: Record<string, number> = {}

        stockData.forEach((stock: CurrentStock) => {
          stocks[stock.produk_gudang_id] = stock.current_stock
        })

        setCurrentStocks(stocks)

        // Extract unique categories
        const uniqueCategories = Array.from(new Set(productsData.map(p => p.category).filter(Boolean)))
        setCategories(uniqueCategories)
      } catch (error) {
        console.error("Error fetching stock data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  // Format number with thousand separators
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('id-ID').format(num)
  }

  // Get stock status
  const getStockStatus = (productId: string | undefined, minStock: number) => {
    if (!productId) return { status: "unknown", label: "Unknown", color: "gray" }

    const currentStock = currentStocks[productId] || 0

    if (currentStock <= 0) {
      return { status: "out", label: "Out of Stock", color: "red" }
    } else if (currentStock < minStock) {
      return { status: "low", label: "Low Stock", color: "orange" }
    } else {
      return { status: "ok", label: "In Stock", color: "green" }
    }
  }

  // Filter and sort products
  const filteredAndSortedProducts = products
    .filter(product => {
      // Search filter
      const searchMatch = !searchTerm ||
        product.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.sku?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.category?.toLowerCase().includes(searchTerm.toLowerCase())

      // Category filter
      const categoryMatch = categoryFilter === "all" || product.category === categoryFilter

      // Stock filter
      let stockMatch = true
      if (stockFilter !== "all") {
        const stockStatus = getStockStatus(product.id, product.min_stock)
        stockMatch = stockStatus.status === stockFilter
      }

      return searchMatch && categoryMatch && stockMatch
    })
    .sort((a, b) => {
      let comparison = 0
      switch (sortBy) {
        case "name":
          comparison = (a.name || "").localeCompare(b.name || "")
          break
        case "category":
          comparison = (a.category || "").localeCompare(b.category || "")
          break
        case "sku":
          comparison = (a.sku || "").localeCompare(b.sku || "")
          break
        case "stock":
          const stockA = currentStocks[a.id || ''] || 0
          const stockB = currentStocks[b.id || ''] || 0
          comparison = stockA - stockB
          break
        case "status":
          const statusA = getStockStatus(a.id, a.min_stock).status
          const statusB = getStockStatus(b.id, b.min_stock).status
          const statusOrder = { "out": 0, "low": 1, "ok": 2 }
          comparison = (statusOrder[statusA as keyof typeof statusOrder] || 3) - (statusOrder[statusB as keyof typeof statusOrder] || 3)
          break
        default:
          comparison = (a.name || "").localeCompare(b.name || "")
      }

      return sortOrder === "asc" ? comparison : -comparison
    })

  // Pagination
  const pagination = usePagination<ProdukGudang>({
    data: filteredAndSortedProducts,
    itemsPerPage: 10
  })

  if (isLoading) {
    return (
      <ProtectedRoute>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <div>
              <h2 className="text-lg font-semibold">Loading Stock Data...</h2>
              <p className="text-muted-foreground">Analyzing your inventory levels</p>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">📦 Stock Management</h1>
            <p className="text-muted-foreground">Monitor and manage your inventory levels with advanced filtering</p>
          </div>
          <div className="flex gap-2 flex-wrap">
          <Button asChild>
            <Link href="/stock/add-movement">
              <Plus className="mr-2 h-4 w-4" />
              Single Movement
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/stock/bulk-movement">
              <Package2 className="mr-2 h-4 w-4" />
              Bulk Movement
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/stock/bulk-movement/history">
              <History className="mr-2 h-4 w-4" />
              Bulk History
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/stock/reports">
              <FileText className="mr-2 h-4 w-4" />
              Reports
            </Link>
          </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Products</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatNumber(products.length)}</div>
              <p className="text-xs text-muted-foreground">
                Across {categories.length} categories
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">In Stock</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {formatNumber(products.filter(p => {
                  const status = getStockStatus(p.id, p.min_stock)
                  return status.status === "ok"
                }).length)}
              </div>
              <p className="text-xs text-muted-foreground">Products available</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Low Stock</CardTitle>
              <TrendingDown className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {formatNumber(products.filter(p => {
                  const status = getStockStatus(p.id, p.min_stock)
                  return status.status === "low"
                }).length)}
              </div>
              <p className="text-xs text-muted-foreground">Need restocking</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Out of Stock</CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {formatNumber(products.filter(p => {
                  const status = getStockStatus(p.id, p.min_stock)
                  return status.status === "out"
                }).length)}
              </div>
              <p className="text-xs text-muted-foreground">Urgent attention</p>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Current Stock Levels
            </CardTitle>
            <CardDescription>
              Search, filter, and manage all products and their current stock levels
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Search and Filters */}
            <div className="space-y-4 mb-6">
              <div className="flex flex-col md:flex-row gap-4">
                {/* Search */}
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search products by name, SKU, or category..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>

                {/* Sort */}
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name">Name</SelectItem>
                    <SelectItem value="category">Category</SelectItem>
                    <SelectItem value="sku">SKU</SelectItem>
                    <SelectItem value="stock">Stock Level</SelectItem>
                    <SelectItem value="status">Stock Status</SelectItem>
                  </SelectContent>
                </Select>

                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
                >
                  {sortOrder === "asc" ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />}
                </Button>
              </div>

              <div className="flex flex-col md:flex-row gap-4">
                {/* Category Filter */}
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className="w-[200px]">
                    <SelectValue placeholder="Filter by category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* Stock Status Filter */}
                <Select value={stockFilter} onValueChange={setStockFilter}>
                  <SelectTrigger className="w-[200px]">
                    <SelectValue placeholder="Filter by stock status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Stock Status</SelectItem>
                    <SelectItem value="ok">In Stock</SelectItem>
                    <SelectItem value="low">Low Stock</SelectItem>
                    <SelectItem value="out">Out of Stock</SelectItem>
                  </SelectContent>
                </Select>

                {/* Clear Filters */}
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchTerm("")
                    setCategoryFilter("all")
                    setStockFilter("all")
                    setSortBy("name")
                    setSortOrder("asc")
                  }}
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Clear Filters
                </Button>
              </div>
            </div>

            {/* Results Summary */}
            <div className="flex justify-between items-center mb-4">
              <p className="text-sm text-muted-foreground">
                Found {filteredAndSortedProducts.length} products
              </p>
            </div>
            {/* Enhanced Table */}
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Product</TableHead>
                    <TableHead>SKU</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Unit</TableHead>
                    <TableHead className="text-right">Min Stock</TableHead>
                    <TableHead className="text-right">Current Stock</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pagination.paginatedData.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                        No products found matching your criteria
                      </TableCell>
                    </TableRow>
                  ) : (
                    pagination.paginatedData.map((product: ProdukGudang) => {
                      const currentStock = currentStocks[product.id || ''] || 0
                      const stockStatus = getStockStatus(product.id, product.min_stock)

                      return (
                        <TableRow
                          key={product.id}
                          className={
                            stockStatus.status === "out"
                              ? "bg-red-50 dark:bg-red-950/20 border-red-200 dark:border-red-800"
                              : stockStatus.status === "low"
                              ? "bg-amber-50 dark:bg-amber-950/20 border-amber-200 dark:border-amber-800"
                              : ""
                          }
                        >
                          <TableCell>
                            <div className="font-medium">{product.name}</div>
                            <div className="text-sm text-muted-foreground">{product.description}</div>
                          </TableCell>
                          <TableCell className="font-mono text-sm">{product.sku}</TableCell>
                          <TableCell>
                            <Badge variant="outline" className="text-xs">
                              {product.category}
                            </Badge>
                          </TableCell>
                          <TableCell>{product.unit}</TableCell>
                          <TableCell className="text-right font-mono">
                            {formatNumber(product.min_stock)}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end gap-2">
                              <span className={`font-mono ${
                                stockStatus.status === "out" ? "text-red-600 dark:text-red-400 font-bold" :
                                stockStatus.status === "low" ? "text-orange-600 dark:text-orange-400" : "text-green-600 dark:text-green-400"
                              }`}>
                                {formatNumber(currentStock)}
                              </span>
                              <Badge
                                variant="outline"
                                className={
                                  stockStatus.status === "out"
                                    ? "bg-red-50 dark:bg-red-950/50 text-red-700 dark:text-red-300 border-red-200 dark:border-red-800"
                                    : stockStatus.status === "low"
                                    ? "bg-amber-50 dark:bg-amber-950/50 text-amber-700 dark:text-amber-300 border-amber-200 dark:border-amber-800"
                                    : "bg-emerald-50 dark:bg-emerald-950/50 text-emerald-700 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800"
                                }
                              >
                                {stockStatus.label}
                              </Badge>
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button variant="outline" size="sm" asChild>
                                <Link href={`/stock/add-movement?product=${product.id}&type=incoming`}>
                                  <ArrowDownCircle className="mr-1 h-4 w-4 text-green-500" />
                                  In
                                </Link>
                              </Button>
                              <Button variant="outline" size="sm" asChild>
                                <Link href={`/stock/add-movement?product=${product.id}&type=outgoing`}>
                                  <ArrowUpCircle className="mr-1 h-4 w-4 text-red-500" />
                                  Out
                                </Link>
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Pagination Controls */}
            {pagination.totalItems > 0 && (
              <div className="mt-4">
                <PaginationControls
                  currentPage={pagination.currentPage}
                  totalPages={pagination.totalPages}
                  totalItems={pagination.totalItems}
                  itemsPerPage={pagination.itemsPerPage}
                  startIndex={pagination.startIndex}
                  endIndex={pagination.endIndex}
                  canGoNext={pagination.canGoNext}
                  canGoPrevious={pagination.canGoPrevious}
                  goToPage={pagination.goToPage}
                  goToNextPage={pagination.goToNextPage}
                  goToPreviousPage={pagination.goToPreviousPage}
                  setItemsPerPage={pagination.setItemsPerPage}
                />
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </ProtectedRoute>
  )
}

