"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { ArrowDownCircle, ArrowUpCircle, Download, Filter, Search, TrendingUp, TrendingDown, Package, AlertTriangle, BarChart3, Calendar } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { ProtectedRoute } from '@/components/auth/protected-route'
import { usePagination } from "@/hooks/use-pagination"
import { PaginationControls } from "@/components/ui/pagination-controls"
import { getProducts, getStockMovements, getCurrentStock } from "@/lib/directus"
import type { ProdukGudang, StockMovement, CurrentStock } from "@/lib/directus"

export default function StockReportsPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [dateRange, setDateRange] = useState("last30")
  const [category, setCategory] = useState("all")
  const [startDate, setStartDate] = useState<string>("")
  const [endDate, setEndDate] = useState<string>("")
  const [searchTerm, setSearchTerm] = useState("")
  const [sortBy, setSortBy] = useState("date")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")
  const [products, setProducts] = useState<ProdukGudang[]>([])
  const [movements, setMovements] = useState<StockMovement[]>([])
  const [currentStocks, setCurrentStocks] = useState<Record<string, number>>({})
  const [categories, setCategories] = useState<string[]>([])
  const [stats, setStats] = useState({
    totalValue: 0,
    totalIncoming: 0,
    totalOutgoing: 0,
    movementsCount: 0
  })

  useEffect(() => {
    async function fetchData() {
      try {
        const [productsData, movementsData, stockData] = await Promise.all([
          getProducts(),
          getStockMovements(),
          getCurrentStock()
        ])

        setProducts(productsData)
        setMovements(movementsData)

        // Process current stocks
        const stocks: Record<string, number> = {}
        stockData.forEach((stock: CurrentStock) => {
          stocks[stock.produk_gudang_id] = stock.current_stock
        })
        setCurrentStocks(stocks)

        // Extract unique categories
        const uniqueCategories = Array.from(new Set(productsData.map(p => p.category).filter(Boolean)))
        setCategories(uniqueCategories)

        // Calculate statistics
        const totalIncoming = movementsData
          .filter(m => m.type === 'incoming')
          .reduce((sum, m) => sum + (m.quantity || 0), 0)

        const totalOutgoing = movementsData
          .filter(m => m.type === 'outgoing')
          .reduce((sum, m) => sum + (m.quantity || 0), 0)

        const totalValue = stockData.reduce((sum, stock) => {
          const product = productsData.find(p => p.id === stock.produk_gudang_id)
          const purchasePrice = product?.purchase_price || 0
          return sum + (stock.current_stock * purchasePrice)
        }, 0)

        setStats({
          totalValue,
          totalIncoming,
          totalOutgoing,
          movementsCount: movementsData.length
        })

      } catch (error) {
        console.error("Error fetching report data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  // Helper function to get date ranges
  const getDateRange = () => {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    switch (dateRange) {
      case "last7":
        return new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
      case "last30":
        return new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
      case "last90":
        return new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000)
      case "custom":
        return startDate ? new Date(startDate) : null
      default:
        return new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
    }
  }

  // Filter and sort movements
  const filteredAndSortedMovements = movements
    .filter(movement => {
      const movementDate = new Date(movement.date)
      const product = products.find(p => p.id === movement.produk_gudang_id)

      // Date filter
      const dateStart = getDateRange()
      const dateEnd = dateRange === "custom" && endDate ? new Date(endDate) : new Date()
      const dateMatch = dateStart ? movementDate >= dateStart && movementDate <= dateEnd : true

      // Category filter
      const categoryMatch = category === "all" || product?.category === category

      // Search filter
      const searchMatch = !searchTerm ||
        product?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        movement.reference_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        movement.notes?.toLowerCase().includes(searchTerm.toLowerCase())

      return dateMatch && categoryMatch && searchMatch
    })
    .sort((a, b) => {
      const productA = products.find(p => p.id === a.produk_gudang_id)
      const productB = products.find(p => p.id === b.produk_gudang_id)

      let comparison = 0
      switch (sortBy) {
        case "date":
          comparison = new Date(a.date).getTime() - new Date(b.date).getTime()
          break
        case "product":
          comparison = (productA?.name || "").localeCompare(productB?.name || "")
          break
        case "quantity":
          comparison = (a.quantity || 0) - (b.quantity || 0)
          break
        case "type":
          comparison = a.type.localeCompare(b.type)
          break
        default:
          comparison = new Date(a.date).getTime() - new Date(b.date).getTime()
      }

      return sortOrder === "asc" ? comparison : -comparison
    })

  // Pagination for movements
  const movementsPagination = usePagination<StockMovement>({
    data: filteredAndSortedMovements,
    itemsPerPage: 15
  })

  // Find low stock products with search filter
  const filteredLowStockProducts = products.filter(product => {
    const currentStock = currentStocks[product.id || ''] || 0
    const isLowStock = currentStock < product.min_stock
    const searchMatch = !searchTerm ||
      product.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.category?.toLowerCase().includes(searchTerm.toLowerCase())
    return isLowStock && searchMatch
  })

  // Pagination for low stock products
  const lowStockPagination = usePagination<ProdukGudang>({
    data: filteredLowStockProducts,
    itemsPerPage: 10
  })

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  // Format number with thousand separators
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('id-ID').format(num)
  }

  const handleExport = () => {
    // Prepare CSV data
    const headers = ["Date", "Product", "Type", "Quantity", "Unit", "Reference", "Category", "Notes"]
    const rows = filteredAndSortedMovements.map((movement: StockMovement) => {
      const product = products.find(p => p.id === movement.produk_gudang_id)
      return [
        new Date(movement.date).toLocaleDateString('id-ID'),
        product?.name || 'Unknown Product',
        movement.type === 'incoming' ? 'Masuk' : 'Keluar',
        movement.quantity || 0,
        product?.unit || 'PCS',
        movement.reference_number || '',
        product?.category || 'Unknown',
        movement.notes || ''
      ]
    })

    // Create CSV content
    const csvContent = [
      headers.join(','),
      ...rows.map((row: any[]) => row.map(cell => `"${cell}"`).join(','))
    ].join('\n')

    // Create and trigger download
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `stock-movements-${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  if (isLoading) {
    return (
      <ProtectedRoute>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <div>
              <h2 className="text-lg font-semibold">Loading Stock Reports...</h2>
              <p className="text-muted-foreground">Analyzing your inventory data</p>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">📊 Stock Reports</h1>
            <p className="text-muted-foreground">Comprehensive analysis of your inventory data</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleExport}>
              <Download className="mr-2 h-4 w-4" />
              Export Data
            </Button>
          </div>
        </div>

        {/* Quick Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Products</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatNumber(products.length)}</div>
              <p className="text-xs text-muted-foreground">
                Across {categories.length} categories
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Stock Value</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(stats.totalValue)}</div>
              <p className="text-xs text-muted-foreground">
                Based on purchase prices
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Low Stock Items</CardTitle>
              <AlertTriangle className="h-4 w-4 text-destructive" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-destructive">{filteredLowStockProducts.length}</div>
              <p className="text-xs text-muted-foreground">
                Items below minimum level
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Movements</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatNumber(stats.movementsCount)}</div>
              <p className="text-xs text-muted-foreground">
                In selected period
              </p>
            </CardContent>
          </Card>
        </div>

      <Tabs defaultValue="movements">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="movements">Stock Movements</TabsTrigger>
          <TabsTrigger value="low-stock">Low Stock</TabsTrigger>
          <TabsTrigger value="summary">Summary</TabsTrigger>
        </TabsList>

        <TabsContent value="movements" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Stock Movement History
              </CardTitle>
              <CardDescription>
                Track all incoming and outgoing stock movements with advanced filtering
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Search and Filters */}
              <div className="space-y-4 mb-6">
                <div className="flex flex-col md:flex-row gap-4">
                  {/* Search */}
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search products, references, or notes..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>

                  {/* Sort */}
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="Sort by" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="date">Date</SelectItem>
                      <SelectItem value="product">Product</SelectItem>
                      <SelectItem value="quantity">Quantity</SelectItem>
                      <SelectItem value="type">Type</SelectItem>
                    </SelectContent>
                  </Select>

                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
                  >
                    {sortOrder === "asc" ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />}
                  </Button>
                </div>

                <div className="flex flex-col md:flex-row gap-4">
                  {/* Date Range */}
                  <div className="flex items-center gap-2">
                    <Select value={dateRange} onValueChange={setDateRange}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select date range" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="last7">Last 7 days</SelectItem>
                        <SelectItem value="last30">Last 30 days</SelectItem>
                        <SelectItem value="last90">Last 90 days</SelectItem>
                        <SelectItem value="custom">Custom range</SelectItem>
                      </SelectContent>
                    </Select>

                    {dateRange === "custom" && (
                      <div className="flex gap-2 items-center">
                        <Input
                          type="date"
                          className="w-[150px]"
                          value={startDate}
                          onChange={(e) => setStartDate(e.target.value)}
                        />
                        <span className="text-muted-foreground">to</span>
                        <Input
                          type="date"
                          className="w-[150px]"
                          value={endDate}
                          onChange={(e) => setEndDate(e.target.value)}
                        />
                      </div>
                    )}
                  </div>

                  {/* Category Filter */}
                  <Select value={category} onValueChange={setCategory}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {categories.map((cat) => (
                        <SelectItem key={cat} value={cat}>
                          {cat}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Results Summary */}
              <div className="flex justify-between items-center mb-4">
                <p className="text-sm text-muted-foreground">
                  Found {filteredAndSortedMovements.length} movements
                </p>
                <Button variant="outline" size="sm" onClick={handleExport}>
                  <Download className="mr-2 h-4 w-4" />
                  Export ({filteredAndSortedMovements.length})
                </Button>
              </div>

              {/* Enhanced Table */}
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[120px]">Date</TableHead>
                      <TableHead>Product</TableHead>
                      <TableHead className="w-[120px]">Type</TableHead>
                      <TableHead className="w-[120px] text-right">Quantity</TableHead>
                      <TableHead className="w-[150px]">Reference</TableHead>
                      <TableHead className="w-[100px]">Category</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {movementsPagination.paginatedData.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                          No movements found matching your criteria
                        </TableCell>
                      </TableRow>
                    ) : (
                      movementsPagination.paginatedData.map((movement: StockMovement) => {
                        const product = products.find(p => p.id === movement.produk_gudang_id)
                        return (
                          <TableRow key={movement.id}>
                            <TableCell className="font-mono text-sm">
                              {new Date(movement.date).toLocaleDateString('id-ID')}
                            </TableCell>
                            <TableCell>
                              <div className="font-medium">{product?.name || 'Unknown Product'}</div>
                              <div className="text-sm text-muted-foreground">{product?.sku}</div>
                            </TableCell>
                            <TableCell>
                              {movement.type === "incoming" ? (
                                <Badge variant="secondary" className="bg-green-50 text-green-700 border-green-200">
                                  <ArrowDownCircle className="h-3 w-3 mr-1" />
                                  Masuk
                                </Badge>
                              ) : (
                                <Badge variant="secondary" className="bg-red-50 text-red-700 border-red-200">
                                  <ArrowUpCircle className="h-3 w-3 mr-1" />
                                  Keluar
                                </Badge>
                              )}
                            </TableCell>
                            <TableCell className="text-right font-mono">
                              <div>{formatNumber(movement.quantity || 0)}</div>
                              <div className="text-sm text-muted-foreground">{product?.unit || 'PCS'}</div>
                            </TableCell>
                            <TableCell className="font-mono text-sm">
                              {movement.reference_number || '-'}
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline" className="text-xs">
                                {product?.category || 'Unknown'}
                              </Badge>
                            </TableCell>
                          </TableRow>
                        )
                      })
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination Controls for Movements */}
              {movementsPagination.totalItems > 0 && (
                <div className="mt-4">
                  <PaginationControls
                    currentPage={movementsPagination.currentPage}
                    totalPages={movementsPagination.totalPages}
                    totalItems={movementsPagination.totalItems}
                    itemsPerPage={movementsPagination.itemsPerPage}
                    startIndex={movementsPagination.startIndex}
                    endIndex={movementsPagination.endIndex}
                    canGoNext={movementsPagination.canGoNext}
                    canGoPrevious={movementsPagination.canGoPrevious}
                    goToPage={movementsPagination.goToPage}
                    goToNextPage={movementsPagination.goToNextPage}
                    goToPreviousPage={movementsPagination.goToPreviousPage}
                    setItemsPerPage={movementsPagination.setItemsPerPage}
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="low-stock" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-destructive" />
                Low Stock Alert
              </CardTitle>
              <CardDescription>
                Products that are below their minimum stock level - immediate attention required
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Search for Low Stock */}
              <div className="mb-6">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search low stock products..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Low Stock Summary */}
              <div className="grid gap-4 md:grid-cols-3 mb-6">
                <Card className="border-destructive/20">
                  <CardContent className="pt-4">
                    <div className="text-2xl font-bold text-destructive">{filteredLowStockProducts.length}</div>
                    <p className="text-sm text-muted-foreground">Items below minimum</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-4">
                    <div className="text-2xl font-bold">
                      {formatCurrency(
                        filteredLowStockProducts.reduce((sum, product) => {
                          const currentStock = currentStocks[product.id || ''] || 0
                          const needed = Math.max(0, product.min_stock - currentStock)
                          return sum + (needed * (product.purchase_price || 0))
                        }, 0)
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">Estimated restock cost</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-4">
                    <div className="text-2xl font-bold">
                      {filteredLowStockProducts.reduce((sum, product) => {
                        const currentStock = currentStocks[product.id || ''] || 0
                        return sum + Math.max(0, product.min_stock - currentStock)
                      }, 0)}
                    </div>
                    <p className="text-sm text-muted-foreground">Total units needed</p>
                  </CardContent>
                </Card>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead className="text-right">Current</TableHead>
                      <TableHead className="text-right">Minimum</TableHead>
                      <TableHead className="text-right">Needed</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {lowStockPagination.paginatedData.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                          {searchTerm ? 'No low stock products found matching your search' : '🎉 All products are well stocked!'}
                        </TableCell>
                      </TableRow>
                    ) : (
                      lowStockPagination.paginatedData.map((product: ProdukGudang) => {
                        const currentStock = currentStocks[product.id || ''] || 0
                        const needed = Math.max(0, product.min_stock - currentStock)
                        const severity = currentStock === 0 ? 'critical' : currentStock < product.min_stock * 0.5 ? 'high' : 'medium'

                        return (
                          <TableRow
                            key={product.id}
                            className={
                              severity === 'critical'
                                ? 'bg-red-50 dark:bg-red-950/20 border-red-200 dark:border-red-800'
                                : severity === 'high'
                                ? 'bg-amber-50 dark:bg-amber-950/20 border-amber-200 dark:border-amber-800'
                                : ''
                            }
                          >
                            <TableCell>
                              <div className="font-medium">{product.name}</div>
                              <div className="text-sm text-muted-foreground">{product.sku}</div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline" className="text-xs">
                                {product.category}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right font-mono">
                              <div className={
                                currentStock === 0
                                  ? 'text-red-600 dark:text-red-400 font-bold'
                                  : 'text-orange-600 dark:text-orange-400'
                              }>
                                {formatNumber(currentStock)}
                              </div>
                              <div className="text-sm text-muted-foreground">{product.unit}</div>
                            </TableCell>
                            <TableCell className="text-right font-mono">
                              <div>{formatNumber(product.min_stock)}</div>
                              <div className="text-sm text-muted-foreground">{product.unit}</div>
                            </TableCell>
                            <TableCell className="text-right font-mono">
                              <div className="font-bold text-red-600 dark:text-red-400">{formatNumber(needed)}</div>
                              <div className="text-sm text-muted-foreground">{product.unit}</div>
                            </TableCell>
                            <TableCell>
                              <Badge
                                variant={severity === 'critical' ? 'destructive' : 'secondary'}
                                className={
                                  severity === 'critical'
                                    ? ''
                                    : severity === 'high'
                                    ? 'bg-orange-50 dark:bg-orange-950/50 text-orange-700 dark:text-orange-300 border-orange-200 dark:border-orange-800'
                                    : 'bg-amber-50 dark:bg-amber-950/50 text-amber-700 dark:text-amber-300 border-amber-200 dark:border-amber-800'
                                }
                              >
                                {severity === 'critical' ? '🚨 Out of Stock' :
                                 severity === 'high' ? '⚠️ Very Low' : '📉 Low Stock'}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right">
                              <Button size="sm" asChild>
                                <a href={`/stock/add-movement?product=${product.id}&type=incoming`}>
                                  Restock
                                </a>
                              </Button>
                            </TableCell>
                          </TableRow>
                        )
                      })
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination Controls for Low Stock */}
              {lowStockPagination.totalItems > 0 && (
                <div className="mt-4">
                  <PaginationControls
                    currentPage={lowStockPagination.currentPage}
                    totalPages={lowStockPagination.totalPages}
                    totalItems={lowStockPagination.totalItems}
                    itemsPerPage={lowStockPagination.itemsPerPage}
                    startIndex={lowStockPagination.startIndex}
                    endIndex={lowStockPagination.endIndex}
                    canGoNext={lowStockPagination.canGoNext}
                    canGoPrevious={lowStockPagination.canGoPrevious}
                    goToPage={lowStockPagination.goToPage}
                    goToNextPage={lowStockPagination.goToNextPage}
                    goToPreviousPage={lowStockPagination.goToPreviousPage}
                    setItemsPerPage={lowStockPagination.setItemsPerPage}
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="summary" className="space-y-6">
          {/* Enhanced Summary Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Products</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatNumber(products.length)}</div>
                <p className="text-xs text-muted-foreground">
                  Across {categories.length} categories
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Stock Value</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(stats.totalValue)}</div>
                <p className="text-xs text-muted-foreground">
                  Based on purchase prices
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Low Stock Items</CardTitle>
                <AlertTriangle className="h-4 w-4 text-destructive" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-destructive">{filteredLowStockProducts.length}</div>
                <p className="text-xs text-muted-foreground">
                  Items below minimum level
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Movements</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatNumber(stats.movementsCount)}</div>
                <p className="text-xs text-muted-foreground">
                  All time movements
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Movement Statistics */}
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                  Incoming Stock
                </CardTitle>
                <CardDescription>Total incoming stock movements</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="text-3xl font-bold text-green-600">
                    {formatNumber(stats.totalIncoming)}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Total units received
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingDown className="h-5 w-5 text-red-600" />
                  Outgoing Stock
                </CardTitle>
                <CardDescription>Total outgoing stock movements</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="text-3xl font-bold text-red-600">
                    {formatNumber(stats.totalOutgoing)}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Total units used
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Category Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Category Breakdown
              </CardTitle>
              <CardDescription>Product distribution by category</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {categories.map((cat) => {
                  const categoryProducts = products.filter(p => p.category === cat)
                  const categoryValue = categoryProducts.reduce((sum, product) => {
                    const stock = currentStocks[product.id || ''] || 0
                    return sum + (stock * (product.purchase_price || 0))
                  }, 0)

                  return (
                    <div key={cat} className="p-4 border rounded-lg">
                      <div className="font-medium">{cat}</div>
                      <div className="text-2xl font-bold">{categoryProducts.length}</div>
                      <div className="text-sm text-muted-foreground">
                        {formatCurrency(categoryValue)} value
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Common tasks and shortcuts</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Button asChild className="h-auto p-4 flex-col">
                  <a href="/stock/add-movement">
                    <Package className="h-6 w-6 mb-2" />
                    <span>Add Movement</span>
                  </a>
                </Button>
                <Button asChild variant="outline" className="h-auto p-4 flex-col">
                  <a href="/stock/bulk-movement">
                    <BarChart3 className="h-6 w-6 mb-2" />
                    <span>Bulk Movement</span>
                  </a>
                </Button>
                <Button asChild variant="outline" className="h-auto p-4 flex-col">
                  <a href="/products/import">
                    <Download className="h-6 w-6 mb-2" />
                    <span>Import Products</span>
                  </a>
                </Button>
                <Button
                  variant="outline"
                  className="h-auto p-4 flex-col"
                  onClick={handleExport}
                >
                  <Download className="h-6 w-6 mb-2" />
                  <span>Export Data</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      </div>
    </ProtectedRoute>
  )
}

