"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { RefreshCw, AlertCircle, CheckCircle, Clock, Zap, Settings } from "lucide-react"
import { toast } from "sonner"

interface SyncMovement {
  id: string
  date: string
  type: 'incoming' | 'outgoing'
  quantity: number
  produk_gudang_id: {
    name: string
    sku: string
  }
  vendor_id?: {
    vendor_name: string
    vendor_code: string
  }
  synced: boolean
  accurate_id?: string
  synced_at?: string
  sync_error?: string
  purchase_price?: number
  total_cost?: number
}

export default function SyncDashboard() {
  const [unsyncedMovements, setUnsyncedMovements] = useState<SyncMovement[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isRetrying, setIsRetrying] = useState(false)
  const [isSyncing, setIsSyncing] = useState<string | null>(null)

  const fetchUnsyncedMovements = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/sync/manual?action=unsynced&limit=50')
      const data = await response.json()

      if (data.success) {
        setUnsyncedMovements(data.data)
      } else {
        toast.error('Failed to fetch unsynced movements')
      }
    } catch (error) {
      console.error('Error fetching unsynced movements:', error)
      toast.error('Error fetching data')
    } finally {
      setIsLoading(false)
    }
  }

  const retryFailedSyncs = async () => {
    try {
      setIsRetrying(true)
      const response = await fetch('/api/sync/manual', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'retry_failed', limit: 10 })
      })

      const data = await response.json()

      if (data.success) {
        toast.success(`Retry completed: ${data.successful} successful, ${data.failed} failed`)
        fetchUnsyncedMovements() // Refresh the list
      } else {
        toast.error('Failed to retry syncs')
      }
    } catch (error) {
      console.error('Error retrying syncs:', error)
      toast.error('Error retrying syncs')
    } finally {
      setIsRetrying(false)
    }
  }

  const syncSingle = async (stockMovementId: string) => {
    try {
      setIsSyncing(stockMovementId)
      const response = await fetch('/api/sync/manual', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'sync_single', stockMovementId })
      })

      const data = await response.json()

      if (data.success) {
        toast.success(`Successfully synced to Accurate: ${data.accurateNo || data.accurateId}`)
        fetchUnsyncedMovements() // Refresh the list
      } else {
        toast.error(`Sync failed: ${data.error}`)
      }
    } catch (error) {
      console.error('Error syncing movement:', error)
      toast.error('Error syncing movement')
    } finally {
      setIsSyncing(null)
    }
  }

  useEffect(() => {
    fetchUnsyncedMovements()
  }, [])

  const getSyncStatusBadge = (movement: SyncMovement) => {
    if (movement.synced) {
      return <Badge variant="default" className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" />Synced</Badge>
    } else if (movement.sync_error) {
      return <Badge variant="destructive"><AlertCircle className="w-3 h-3 mr-1" />Failed</Badge>
    } else {
      return <Badge variant="secondary"><Clock className="w-3 h-3 mr-1" />Pending</Badge>
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Accurate Sync Dashboard</h1>
          <p className="text-muted-foreground">Monitor and manage stock movement synchronization with Accurate</p>
        </div>
        <div className="flex gap-2">
          <Button asChild variant="outline">
            <a href="/api/accurate/auth" target="_blank" rel="noopener noreferrer">
              <Settings className="w-4 h-4 mr-2" />
              Setup OAuth
            </a>
          </Button>
          <Button
            onClick={fetchUnsyncedMovements}
            disabled={isLoading}
            variant="outline"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            onClick={retryFailedSyncs}
            disabled={isRetrying}
            variant="default"
          >
            <Zap className={`w-4 h-4 mr-2 ${isRetrying ? 'animate-pulse' : ''}`} />
            {isRetrying ? 'Retrying...' : 'Retry Failed'}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Unsynced</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{unsyncedMovements.length}</div>
            <p className="text-xs text-muted-foreground">
              Stock movements pending sync
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Failed Syncs</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {unsyncedMovements.filter(m => m.sync_error).length}
            </div>
            <p className="text-xs text-muted-foreground">
              Movements with sync errors
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Syncs</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {unsyncedMovements.filter(m => !m.sync_error && !m.synced).length}
            </div>
            <p className="text-xs text-muted-foreground">
              Movements waiting to sync
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Unsynced Stock Movements</CardTitle>
          <CardDescription>Stock movements that need to be synchronized with Accurate</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-8">
              <RefreshCw className="w-6 h-6 animate-spin" />
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Product</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Vendor</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Error</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {unsyncedMovements.map((movement) => (
                  <TableRow key={movement.id}>
                    <TableCell>{new Date(movement.date).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{movement.produk_gudang_id?.name}</div>
                        <div className="text-sm text-muted-foreground">{movement.produk_gudang_id?.sku}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={movement.type === 'incoming' ? 'default' : 'secondary'}>
                        {movement.type}
                      </Badge>
                    </TableCell>
                    <TableCell>{movement.quantity.toLocaleString()}</TableCell>
                    <TableCell>
                      {movement.vendor_id ? (
                        <div>
                          <div className="font-medium">{movement.vendor_id.vendor_name}</div>
                          <div className="text-sm text-muted-foreground">{movement.vendor_id.vendor_code}</div>
                        </div>
                      ) : (
                        '-'
                      )}
                    </TableCell>
                    <TableCell>
                      {movement.total_cost ? (
                        `Rp ${movement.total_cost.toLocaleString('id-ID')}`
                      ) : (
                        '-'
                      )}
                    </TableCell>
                    <TableCell>{getSyncStatusBadge(movement)}</TableCell>
                    <TableCell>
                      {movement.sync_error ? (
                        <div className="text-sm text-red-600 max-w-xs truncate" title={movement.sync_error}>
                          {movement.sync_error}
                        </div>
                      ) : (
                        '-'
                      )}
                    </TableCell>
                    <TableCell>
                      <Button
                        size="sm"
                        onClick={() => syncSingle(movement.id)}
                        disabled={isSyncing === movement.id || movement.synced}
                      >
                        {isSyncing === movement.id ? (
                          <RefreshCw className="w-3 h-3 animate-spin" />
                        ) : (
                          'Sync'
                        )}
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
                {unsyncedMovements.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-8 text-muted-foreground">
                      No unsynced movements found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
