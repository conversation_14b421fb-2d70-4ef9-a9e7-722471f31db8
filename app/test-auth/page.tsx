'use client'

import { useAuth } from '@/lib/auth'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ProtectedRoute } from '@/components/auth/protected-route'

export default function TestAuthPage() {
  const { user, loading, logout, error } = useAuth()

  return (
    <ProtectedRoute>
      <div className="container py-10">
        <h1 className="text-3xl font-bold mb-6">Authentication Test Page</h1>
        
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Authentication Status</CardTitle>
              <CardDescription>
                Current authentication state
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="font-medium">Loading State:</p>
                <p className="text-sm text-gray-600">{loading ? 'Loading...' : 'Loaded'}</p>
              </div>
              
              <div>
                <p className="font-medium">User Information:</p>
                {user ? (
                  <div className="text-sm text-gray-600">
                    <p>ID: {user.id}</p>
                    <p>Name: {user.first_name} {user.last_name}</p>
                    <p>Email: {user.email}</p>
                    <p>Role: {user.role}</p>
                  </div>
                ) : (
                  <p className="text-sm text-gray-600">Not logged in</p>
                )}
              </div>

              {error && (
                <div>
                  <p className="font-medium text-red-600">Error:</p>
                  <p className="text-sm text-red-600">{error}</p>
                </div>
              )}

              <Button 
                onClick={() => logout()}
                variant="destructive"
                className="w-full"
              >
                Logout
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Token Information</CardTitle>
              <CardDescription>
                Current token state
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <p className="font-medium">Token Status:</p>
                  <p className="text-sm text-gray-600">
                    {typeof window !== 'undefined' && localStorage.getItem('directus_token')
                      ? 'Token exists'
                      : 'No token found'}
                  </p>
                </div>

                <div>
                  <p className="font-medium">Token Value:</p>
                  <p className="text-sm text-gray-600 break-all">
                    {typeof window !== 'undefined' && localStorage.getItem('directus_token')
                      ? `${localStorage.getItem('directus_token')?.slice(0, 20)}...`
                      : 'No token'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </ProtectedRoute>
  )
} 