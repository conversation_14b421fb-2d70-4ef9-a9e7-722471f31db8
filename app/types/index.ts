export interface KangIder {
  id: string;
  status: string;
  nama: string;
  notelp: string;
  norekening: string;
  nama_bank: string;
  iduser: string[];
}

export interface SalesPlanned {
  id: string;
  kangider_id: string;
  date: string;
  KS: number;
  AM: number;
  VL: number;
  SC: number;
  CK: number;
  ESS: number;
  SC_250: number;
  VL_250: number;
  HZ_250: number;
  CRK_250: number;
  total_cup: number;
  status: 'draft' | 'published';
}

export interface SalesTarget {
  KS: number;
  AM: number;
  VL: number;
  SC: number;
  CK: number;
  ESS: number;
  SC_250: number;
  VL_250: number;
  HZ_250: number;
  CRK_250: number;
} 