"use client"

import { useState, useEffect, use } from "react"
import { useRout<PERSON> } from "next/navigation"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { Button } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "@/hooks/use-toast"
import { Building2 } from "lucide-react"
import { fetchVendor, editVendor } from "@/app/actions"
import type { Vendor } from "@/lib/directus"

const formSchema = z.object({
  vendor_code: z.string().min(1, {
    message: "Vendor code is required",
  }),
  vendor_name: z.string().min(1, {
    message: "Vendor name is required",
  }),
  tax_id: z.string().optional(),
  phone: z.string().optional(),
})

export default function EditVendorPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params)
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [vendor, setVendor] = useState<Vendor | null>(null)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      vendor_code: "",
      vendor_name: "",
      tax_id: "",
      phone: "",
    },
  })

  useEffect(() => {
    loadVendor()
  }, [])

  async function loadVendor() {
    try {
      const result = await fetchVendor(resolvedParams.id)
      if (result.success) {
        const vendorData = result.data
        setVendor(vendorData)
        form.reset({
          vendor_code: vendorData.vendor_code,
          vendor_name: vendorData.vendor_name,
          tax_id: vendorData.tax_id || "",
          phone: vendorData.phone || "",
        })
      } else {
        toast({
          title: "Error",
          description: result.error,
          variant: "destructive",
        })
        router.push("/vendors")
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load vendor",
        variant: "destructive",
      })
      router.push("/vendors")
    } finally {
      setIsLoading(false)
    }
  }

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsSubmitting(true)
    try {
      const result = await editVendor(resolvedParams.id, values)
      
      if (result.success) {
        toast({
          title: "Success",
          description: "Vendor updated successfully",
        })
        router.push("/vendors")
      } else {
        toast({
          title: "Error",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update vendor",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Edit Vendor</h1>
          <p className="text-muted-foreground">Loading vendor...</p>
        </div>
      </div>
    )
  }

  if (!vendor) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Edit Vendor</h1>
          <p className="text-muted-foreground">Vendor not found</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Edit Vendor</h1>
        <p className="text-muted-foreground">Update vendor information</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Vendor Information
          </CardTitle>
          <CardDescription>Update the vendor details below</CardDescription>
        </CardHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="vendor_code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Vendor Code *</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., V.00001" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="vendor_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Vendor Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., ABC Supplier" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="tax_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tax ID (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., *********" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., +62 812 3456 7890" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" type="button" onClick={() => router.back()}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Updating..." : "Update Vendor"}
              </Button>
            </CardFooter>
          </form>
        </Form>
      </Card>
    </div>
  )
}
