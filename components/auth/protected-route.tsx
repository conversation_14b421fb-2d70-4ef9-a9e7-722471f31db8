'use client'

import { useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useAuth } from '@/lib/auth'

export function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { user, loading } = useAuth()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    if (!loading) {
      const token = localStorage.getItem('directus_token')
      if (!token || !user) {
        // Store the current path to redirect back after login
        const returnTo = pathname !== '/login' ? pathname : '/dashboard'
        window.location.href = `/login?from=${encodeURIComponent(returnTo)}`
      }
    }
  }, [user, loading, pathname])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return <>{children}</>
} 