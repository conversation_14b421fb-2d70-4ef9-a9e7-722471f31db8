'use client'

import { ThemeProvider } from "@/components/theme-provider"
import { AuthProvider } from "@/lib/auth"
import { <PERSON><PERSON> } from "@/components/layout/header"
import { usePathname } from "next/navigation"

export function Providers({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()
  const isLoginPage = pathname === '/login'

  return (
    <AuthProvider>
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
        <div className="flex min-h-screen flex-col">
          {!isLoginPage && <Header />}
          <main className={`flex-1 ${!isLoginPage ? 'container mx-auto px-4 sm:px-6 py-6 md:py-10' : ''}`}>
            {children}
          </main>
        </div>
      </ThemeProvider>
    </AuthProvider>
  )
} 