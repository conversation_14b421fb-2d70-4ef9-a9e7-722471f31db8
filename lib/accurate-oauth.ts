import { createDirectus, rest, readItems, updateItem, createItem, authentication } from '@directus/sdk'

// OAuth Token interface
export interface AccurateOAuthToken {
  id?: string
  access_token: string
  refresh_token: string
  token_type: string
  scope: string
  expires_at: string
  host?: string
  session?: string
  db_id?: string
  db_alias?: string
  user_id?: string
  created_at?: string
  updated_at?: string
}

// Create Directus client
const getDirectusClient = () => {
  const directusUrl = process.env.NEXT_PUBLIC_DIRECTUS_URL || 'http://localhost:8055'
  const directusToken = process.env.NEXT_PUBLIC_DIRECTUS_TOKEN || process.env.DIRECTUS_TOKEN

  if (!directusToken) {
    throw new Error('NEXT_PUBLIC_DIRECTUS_TOKEN or DIRECTUS_TOKEN not found in environment variables')
  }

  const client = createDirectus(directusUrl)
    .with(rest())
    .with(authentication())

  // Set the token manually
  client.setToken(directusToken)

  return client
}

export class AccurateOAuthManager {
  private directus: any

  constructor() {
    this.directus = getDirectusClient()
  }

  /**
   * Save OAuth tokens to database
   */
  async saveTokens(tokens: Partial<AccurateOAuthToken>): Promise<AccurateOAuthToken> {
    try {
      const tokenData = {
        ...tokens,
        expires_at: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(), // 15 days
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      const result = await this.directus.request(
        (createItem as any)('AccurateOAuthTokens', tokenData)
      )

      return result
    } catch (error) {
      console.error('Error saving OAuth tokens:', error)
      throw error
    }
  }

  /**
   * Get current valid tokens
   */
  async getCurrentTokens(): Promise<AccurateOAuthToken | null> {
    try {
      const tokens = await this.directus.request(
        (readItems as any)('AccurateOAuthTokens', {
          filter: {
            expires_at: { _gt: new Date().toISOString() }
          },
          sort: ['-created_at'],
          limit: 1
        })
      )

      return tokens[0] || null
    } catch (error) {
      console.error('Error fetching OAuth tokens:', error)
      return null
    }
  }

  /**
   * Update tokens (for refresh)
   */
  async updateTokens(id: string, tokens: Partial<AccurateOAuthToken>): Promise<AccurateOAuthToken> {
    try {
      const tokenData = {
        ...tokens,
        expires_at: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(), // 15 days
        updated_at: new Date().toISOString()
      }

      const result = await this.directus.request(
        (updateItem as any)('AccurateOAuthTokens', id, tokenData)
      )

      return result
    } catch (error) {
      console.error('Error updating OAuth tokens:', error)
      throw error
    }
  }

  /**
   * Check if tokens need refresh (expire in next 24 hours)
   */
  async needsRefresh(): Promise<boolean> {
    const tokens = await this.getCurrentTokens()
    if (!tokens) return true

    const expiresAt = new Date(tokens.expires_at)
    const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000)

    return expiresAt <= tomorrow
  }
}

// Export singleton instance
export const oauthManager = new AccurateOAuthManager()
