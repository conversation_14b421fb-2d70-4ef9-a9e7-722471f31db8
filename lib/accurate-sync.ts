import { createDirectus, rest, readItems, updateItem, authentication } from '@directus/sdk'
import { format } from 'date-fns-tz'
import axios from 'axios'
import { oauthManager, AccurateOAuthToken } from './accurate-oauth'

export interface SyncResult {
  success: boolean
  accurateId?: string
  accurateNo?: string
  error?: string
}

export class AccurateSync {
  private directus: any

  constructor() {
    // Initialize Directus client with token
    const directusUrl = process.env.NEXT_PUBLIC_DIRECTUS_URL || 'http://localhost:8055'
    const directusToken = process.env.DIRECTUS_TOKEN || process.env.NEXT_PUBLIC_DIRECTUS_TOKEN

    if (!directusToken) {
      throw new Error('NEXT_PUBLIC_DIRECTUS_TOKEN or DIRECTUS_TOKEN not found in environment variables')
    }

    this.directus = createDirectus(directusUrl)
      .with(rest())
      .with(authentication())

    // Set the token manually
    this.directus.setToken(directusToken)
  }

  /**
   * Sync single stock movement to Accurate
   */
  async syncStockMovement(stockMovementId: string): Promise<SyncResult> {
    try {
      console.log(`🔄 Syncing stock movement: ${stockMovementId}`)

      // Get stock movement with relations
      const stockMovement = await this.getStockMovementWithRelations(stockMovementId)
      if (!stockMovement) {
        throw new Error('Stock movement not found')
      }

      // Type assertion to access sync fields
      const movement = stockMovement as any

      // Check if already synced
      if (movement.synced) {
        return {
          success: true,
          accurateId: movement.accurate_id,
          error: 'Already synced'
        }
      }

      // Validate required data
      if (movement.type === 'incoming' && !movement.vendor_id) {
        throw new Error('Vendor required for incoming stock movement')
      }

      // Build payload and sync
      let result: SyncResult
      if (stockMovement.type === 'incoming') {
        result = await this.createPurchaseInvoice(stockMovement)
      } else {
        result = await this.createJobOrder(stockMovement)
      }

      // Update sync status
      await this.updateSyncStatus(stockMovementId, result)

      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      console.error(`❌ Sync failed for ${stockMovementId}:`, errorMessage)

      // Update error status
      await this.updateSyncStatus(stockMovementId, {
        success: false,
        error: errorMessage
      })

      return {
        success: false,
        error: errorMessage
      }
    }
  }

  /**
   * Retry failed syncs
   */
  async retryFailedSyncs(limit: number = 10): Promise<SyncResult[]> {
    try {
      console.log(`🔄 Retrying failed syncs (limit: ${limit})`)

      // Get failed stock movements
      const failedMovements = await this.directus.request(
        (readItems as any)('StockMovement', {
          filter: {
            synced: { _eq: false },
            sync_error: { _nnull: true }
          },
          limit,
          sort: ['-created_at']
        })
      )

      console.log(`Found ${failedMovements.length} failed movements to retry`)

      const results: SyncResult[] = []
      for (const movement of failedMovements) {
        if (movement.id) {
          const result = await this.syncStockMovement(movement.id)
          results.push(result)

          // Add delay between requests to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      }

      return results
    } catch (error) {
      console.error('Error retrying failed syncs:', error)
      return []
    }
  }

  /**
   * Get unsynced stock movements
   */
  async getUnsyncedMovements(limit: number = 50) {
    try {
      console.log('🔍 Fetching unsynced movements with improved filter')

      // Debug environment variables
      const directusToken = process.env.DIRECTUS_TOKEN || process.env.NEXT_PUBLIC_DIRECTUS_TOKEN
      const directusUrl = process.env.NEXT_PUBLIC_DIRECTUS_URL || 'http://localhost:8055'

      console.log('🔑 Debug auth info:', {
        hasDirectusToken: !!process.env.DIRECTUS_TOKEN,
        hasNextPublicToken: !!process.env.NEXT_PUBLIC_DIRECTUS_TOKEN,
        tokenUsed: directusToken?.substring(0, 10) + '...',
        directusUrl
      })

      // Ensure token is set
      if (directusToken) {
        this.directus.setToken(directusToken)
      }

      const result = await this.directus.request(
        (readItems as any)('StockMovement', {
          filter: {
            _or: [
              // Belum di-sync sama sekali
              { synced: { _eq: false } },
              // Sudah di-sync tapi tidak ada accurate_id (sync gagal)
              {
                synced: { _eq: true },
                accurate_id: { _null: true }
              }
            ]
          },
          limit,
          sort: ['-date_created'],
          fields: [
            '*',
            'produk_gudang_id.name',
            'produk_gudang_id.sku',
            'produk_gudang_id.unit',
            'unit_used.unit_name',
            'vendor_id.vendor_name',
            'vendor_id.vendor_code'
          ]
        })
      )

      console.log('📊 Found unsynced movements:', result.length)
      return result
    } catch (error) {
      console.error('❌ Error fetching unsynced movements:', error)
      console.error('❌ Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      })
      return []
    }
  }

  /**
   * Sync bulk movements with same reference number as one invoice
   */
  async syncBulkMovements(referenceNumber: string): Promise<SyncResult> {
    try {
      console.log(`🔄 Syncing bulk movements with reference: ${referenceNumber}`)

      // Get all movements with the same reference number
      const movements = await this.directus.request(
        (readItems as any)('StockMovement', {
          filter: {
            reference_number: { _eq: referenceNumber },
            synced: { _eq: false }
          },
          fields: [
            '*',
            'produk_gudang_id.name',
            'produk_gudang_id.sku',
            'produk_gudang_id.unit',
            'unit_used.unit_name',
            'vendor_id.vendor_name',
            'vendor_id.vendor_code'
          ]
        })
      )

      if (!movements || movements.length === 0) {
        return {
          success: false,
          error: 'No unsynced movements found with this reference number'
        }
      }

      console.log(`📦 Found ${movements.length} movements to sync as bulk`)

      // Group by type (incoming/outgoing)
      const incomingMovements = movements.filter((m: any) => m.type === 'incoming')
      const outgoingMovements = movements.filter((m: any) => m.type === 'outgoing')

      let result: SyncResult = { success: true }

      // Sync incoming movements as one Purchase Invoice
      if (incomingMovements.length > 0) {
        result = await this.createBulkPurchaseInvoice(incomingMovements)
        if (result.success) {
          // Update all incoming movements with same accurate_id
          await this.updateBulkSyncStatus(incomingMovements.map((m: any) => m.id), result)
        }
      }

      // Sync outgoing movements as one Job Order
      if (outgoingMovements.length > 0) {
        const jobOrderResult = await this.createBulkJobOrder(outgoingMovements)
        if (jobOrderResult.success) {
          // Update all outgoing movements with same accurate_id
          await this.updateBulkSyncStatus(outgoingMovements.map((m: any) => m.id), jobOrderResult)
        } else if (result.success) {
          // If incoming succeeded but outgoing failed
          result = jobOrderResult
        }
      }

      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      console.error(`❌ Bulk sync failed for ${referenceNumber}:`, errorMessage)
      return {
        success: false,
        error: errorMessage
      }
    }
  }

  /**
   * Create Purchase Invoice in Accurate
   */
  private async createPurchaseInvoice(stockMovement: any): Promise<SyncResult> {
    const vendor = stockMovement.vendor_id
    const product = stockMovement.produk_gudang_id

    // Calculate amounts
    const quantity = stockMovement.quantity_converted || stockMovement.quantity
    const unitPrice = stockMovement.purchase_price || 0

    // Debug unit name resolution
    console.log('🔍 Debug unit name resolution:', {
      base_unit_label: stockMovement.base_unit_label,
      unit_used: stockMovement.unit_used,
      unit_used_unit_name: stockMovement.unit_used?.unit_name,
      product_unit: product?.unit,
      fallback: 'PCS'
    })

    const unitName = stockMovement.base_unit_label || stockMovement.unit_used?.unit_name || product?.unit || 'PCS'
    console.log('📝 Final unit name used:', unitName)

    // Format date for Accurate (DD/MM/YYYY)
    const transDate = new Date(stockMovement.date).toLocaleDateString('id-ID', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })

    const payload = {
      'branchId': '50', // Branch ID sesuai format Accurate
      'vendorNo': vendor?.vendor_code || 'VENDOR001',
      'transDate': transDate,
      'useAutoNumber': 'true', // Gunakan auto-number Accurate
      'description': stockMovement.notes || `Stock incoming: ${product?.name}`,
      'detailItem[0].itemNo': product?.sku || 'ITEM001',
      'detailItem[0].quantity': quantity.toString(),
      'detailItem[0].unitPrice': unitPrice.toString(),
      'detailItem[0].itemUnitName': unitName,
      'taxable': 'true',
      'inclusiveTax': 'false',
      'saveAsStatusType': 'DRAFT' // Save as draft status untuk approval workflow
    }

    return await this.sendToAccurate('purchase-invoice/save.do', payload)
  }

  /**
   * Create Job Order in Accurate
   */
  private async createJobOrder(stockMovement: any): Promise<SyncResult> {
    const product = stockMovement.produk_gudang_id

    // Calculate amounts
    const quantity = stockMovement.quantity_converted || stockMovement.quantity
    const unitPrice = stockMovement.purchase_price || 0

    // Debug unit name resolution
    console.log('🔍 Debug unit name resolution for Job Order:', {
      base_unit_label: stockMovement.base_unit_label,
      unit_used: stockMovement.unit_used,
      unit_used_unit_name: stockMovement.unit_used?.unit_name,
      product_unit: product?.unit,
      fallback: 'PCS'
    })

    const unitName = stockMovement.base_unit_label || stockMovement.unit_used?.unit_name || product?.unit || 'PCS'
    console.log('📝 Final unit name used for Job Order:', unitName)

    // Format date for Accurate (DD/MM/YYYY)
    const transDate = new Date(stockMovement.date).toLocaleDateString('id-ID', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })

    const payload = {
      'branchId': '50', // Branch ID sesuai format Accurate
      // 'customerNo': 'C.00001', // Gunakan customer default untuk Job Order
      'transDate': transDate,
      'useAutoNumber': 'true', // Gunakan auto-number Accurate
      'description': stockMovement.notes || `Stock outgoing: ${product?.name}`,
      'detailItem[0].itemNo': product?.sku || 'ITEM001',
      'detailItem[0].quantity': quantity.toString(),
      'detailItem[0].unitPrice': unitPrice.toString(),
      'detailItem[0].itemUnitName': unitName,
      'referenceNo': stockMovement.reference_number || '',
      'saveAsStatusType': 'DRAFT' // Save as draft status untuk approval workflow
    }

    return await this.sendToAccurate('job-order/save.do', payload)
  }

  /**
   * Create Bulk Purchase Invoice in Accurate (multiple items in one invoice)
   */
  private async createBulkPurchaseInvoice(movements: any[]): Promise<SyncResult> {
    if (!movements || movements.length === 0) {
      return { success: false, error: 'No movements provided' }
    }

    const firstMovement = movements[0]
    const vendor = firstMovement.vendor_id

    // Format date for Accurate (DD/MM/YYYY)
    const transDate = new Date(firstMovement.date).toLocaleDateString('id-ID', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })

    // Build payload with multiple items
    const payload: any = {
      'branchId': '50',
      'vendorNo': vendor?.vendor_code || 'VENDOR001',
      'transDate': transDate,
      'useAutoNumber': 'true',
      'description': firstMovement.bulk_movement_title || `Bulk Purchase - ${firstMovement.reference_number}`,
      'referenceNo': firstMovement.reference_number || '',
      'taxable': 'true',
      'inclusiveTax': 'false',
      'saveAsStatusType': 'DRAFT'
    }

    // Add each movement as a detail item
    movements.forEach((movement, index) => {
      const product = movement.produk_gudang_id
      const quantity = movement.quantity_converted || movement.quantity
      const unitPrice = movement.purchase_price || 0
      const unitName = movement.base_unit_label || movement.unit_used?.unit_name || product?.unit || 'PCS'

      payload[`detailItem[${index}].itemNo`] = product?.sku || 'ITEM001'
      payload[`detailItem[${index}].quantity`] = quantity.toString()
      payload[`detailItem[${index}].unitPrice`] = unitPrice.toString()
      payload[`detailItem[${index}].itemUnitName`] = unitName
    })

    console.log(`📦 Creating bulk Purchase Invoice with ${movements.length} items`)
    return await this.sendToAccurate('purchase-invoice/save.do', payload)
  }

  /**
   * Create Bulk Job Order in Accurate (multiple items in one job order)
   */
  private async createBulkJobOrder(movements: any[]): Promise<SyncResult> {
    if (!movements || movements.length === 0) {
      return { success: false, error: 'No movements provided' }
    }

    const firstMovement = movements[0]

    // Format date for Accurate (DD/MM/YYYY)
    const transDate = new Date(firstMovement.date).toLocaleDateString('id-ID', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })

    // Build payload with multiple items
    const payload: any = {
      'branchId': '50',
      'transDate': transDate,
      'useAutoNumber': 'true',
      'description': firstMovement.bulk_movement_title || `Bulk Job Order - ${firstMovement.reference_number}`,
      'referenceNo': firstMovement.reference_number || '',
      'saveAsStatusType': 'DRAFT'
    }

    // Add each movement as a detail item
    movements.forEach((movement, index) => {
      const product = movement.produk_gudang_id
      const quantity = movement.quantity_converted || movement.quantity
      const unitPrice = movement.purchase_price || 0
      const unitName = movement.base_unit_label || movement.unit_used?.unit_name || product?.unit || 'PCS'

      payload[`detailItem[${index}].itemNo`] = product?.sku || 'ITEM001'
      payload[`detailItem[${index}].quantity`] = quantity.toString()
      payload[`detailItem[${index}].unitPrice`] = unitPrice.toString()
      payload[`detailItem[${index}].itemUnitName`] = unitName
    })

    console.log(`📦 Creating bulk Job Order with ${movements.length} items`)
    return await this.sendToAccurate('job-order/save.do', payload)
  }

  /**
   * Update sync status for multiple movements
   */
  private async updateBulkSyncStatus(ids: string[], result: SyncResult) {
    try {
      const updatePromises = ids.map(id =>
        this.directus.request(
          (updateItem as any)('StockMovement', id, {
            synced: result.success,
            accurate_id: result.accurateId || null,
            synced_at: new Date().toISOString(),
            sync_error: result.error || null
          })
        )
      )

      await Promise.all(updatePromises)
      console.log(`✅ Updated sync status for ${ids.length} movements`)
    } catch (error) {
      console.error('Error updating bulk sync status:', error)
      throw error
    }
  }

  /**
   * Convert payload to form data for Accurate API
   */
  private convertToFormData(payload: any): URLSearchParams {
    const formData = new URLSearchParams()

    // Add all fields directly (payload sudah dalam format flat)
    Object.keys(payload).forEach(key => {
      if (payload[key] !== undefined && payload[key] !== null) {
        formData.append(key, payload[key].toString())
      }
    })

    return formData
  }

  /**
   * Send request to Accurate API using OAuth tokens
   */
  private async sendToAccurate(endpoint: string, payload: any): Promise<SyncResult> {
    try {
      console.log(`📤 Sending to Accurate: ${endpoint}`)
      console.log('Payload:', JSON.stringify(payload, null, 2))

      // Get current OAuth tokens
      const tokens = await oauthManager.getCurrentTokens()
      if (!tokens) {
        throw new Error('No valid OAuth tokens found. Please authenticate with Accurate first.')
      }

      // Check if tokens need refresh
      if (await oauthManager.needsRefresh()) {
        console.log('🔄 Tokens need refresh, attempting refresh...')
        try {
          await this.refreshTokens()
          // Get refreshed tokens
          const refreshedTokens = await oauthManager.getCurrentTokens()
          if (!refreshedTokens) {
            throw new Error('Failed to refresh tokens')
          }
          // Use refreshed tokens
          Object.assign(tokens, refreshedTokens)
        } catch (refreshError) {
          console.error('❌ Failed to refresh tokens:', refreshError)
          throw new Error('Authentication expired. Please re-authenticate with Accurate.')
        }
      }

      // Build API URL
      const apiUrl = `${tokens.host}/accurate/api/${endpoint}`

      console.log('🔑 Using OAuth tokens:', {
        host: tokens.host,
        session: tokens.session?.substring(0, 10) + '...',
        tokenType: tokens.token_type
      })

      // Convert payload to form data with array notation
      const formData = this.convertToFormData(payload)
      console.log('📝 Form data:', formData.toString())

      // Make API request with OAuth using form data
      const response = await axios.post(apiUrl, formData, {
        headers: {
          'Authorization': `Bearer ${tokens.access_token}`,
          'X-Session-ID': tokens.session,
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      })

      console.log('📥 Accurate response:', {
        status: response.status,
        statusText: response.statusText,
        data: response.data
      })

      // Check if response is HTML (login page redirect)
      if (typeof response.data === 'string' && response.data.includes('<html>')) {
        console.error('❌ Received HTML response - likely authentication issue')
        return {
          success: false,
          error: 'Authentication failed - received login page instead of API response'
        }
      }

      // Check for successful API response
      if (response.data && typeof response.data === 'object') {
        // Accurate API response format: { s: true/false, d: data, r: result }
        if (response.data.s === true) {
          return {
            success: true,
            accurateId: response.data.r?.id?.toString() || null,
            accurateNo: response.data.r?.number || response.data.r?.billNumber || null
          }
        } else {
          // API call successful but business logic failed
          return {
            success: false,
            error: Array.isArray(response.data.d) ? response.data.d.join(', ') : 'Unknown error from Accurate'
          }
        }
      } else {
        return {
          success: false,
          error: 'No valid data returned from Accurate API'
        }
      }
    } catch (error: any) {
      console.error('Accurate API error:', error.response?.data || error.message)

      // Handle 401 Unauthorized - try to refresh tokens
      if (error.response?.status === 401) {
        console.log('🔄 Received 401, attempting token refresh...')
        try {
          await this.refreshTokens()
          // Retry the request once with refreshed tokens
          return await this.sendToAccurate(endpoint, payload)
        } catch (refreshError) {
          console.error('❌ Failed to refresh tokens after 401:', refreshError)
          return {
            success: false,
            error: 'Authentication expired. Please re-authenticate with Accurate.'
          }
        }
      }

      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error'
      }
    }
  }

  /**
   * Refresh OAuth tokens
   */
  private async refreshTokens(): Promise<void> {
    const response = await axios.post(`${process.env.NEXT_PUBLIC_BASE_URL}/api/accurate/refresh`)
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to refresh tokens')
    }
  }


  /**
   * Get stock movement with relations
   */
  private async getStockMovementWithRelations(id: string) {
    try {
      const movements = await this.directus.request(
        (readItems as any)('StockMovement', {
          filter: { id: { _eq: id } },
          fields: [
            '*',
            'produk_gudang_id.name',
            'produk_gudang_id.sku',
            'produk_gudang_id.unit',
            'unit_used.unit_name', // Tambahkan unit name dari unit_used
            'vendor_id.vendor_name',
            'vendor_id.vendor_code',
            'vendor_id.tax_id'
          ]
        })
      )
      return movements[0] || null
    } catch (error) {
      console.error('Error fetching stock movement:', error)
      return null
    }
  }

  /**
   * Update sync status
   */
  private async updateSyncStatus(id: string, result: SyncResult) {
    try {
      await this.directus.request(
        (updateItem as any)('StockMovement', id, {
          synced: result.success,
          accurate_id: result.accurateId || null,
          synced_at: new Date().toISOString(),
          sync_error: result.error || null
        })
      )
    } catch (error) {
      console.error('Error updating sync status:', error)
      throw error
    }
  }
}

// Export singleton instance
export const accurateSync = new AccurateSync()
