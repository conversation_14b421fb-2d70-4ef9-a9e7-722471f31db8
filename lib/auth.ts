'use client'

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { createDirectus, authentication, rest, readMe } from '@directus/sdk'
import { useRouter } from 'next/navigation'

interface User {
  id: string
  email: string
  first_name: string
  last_name: string
  role: string
}

interface AuthContextType {
  user: User | null
  loading: boolean
  login: (email: string, password: string, returnTo?: string) => Promise<void>
  logout: () => void
  error: string | null
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  login: async () => {},
  logout: () => {},
  error: null
})

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  const directus = createDirectus(process.env.NEXT_PUBLIC_DIRECTUS_URL!)
    .with(authentication())
    .with(rest())

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const token = localStorage.getItem('directus_token')
      if (token) {
        directus.setToken(token)
        const me = await directus.request(readMe())
        setUser(me as User)
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      localStorage.removeItem('directus_token')
    } finally {
      setLoading(false)
    }
  }

  const login = async (email: string, password: string, returnTo: string = '/dashboard') => {
    try {
      setError(null)
      const response = await directus.login(email, password)
      if (response.access_token) {
        localStorage.setItem('directus_token', response.access_token)
        const me = await directus.request(readMe())
        setUser(me as User)
        // Force a hard navigation to returnTo or dashboard
        window.location.href = returnTo
      }
    } catch (error: any) {
      setError(error.message || 'Login failed')
      throw error
    }
  }

  const logout = () => {
    // Simply clear the token and user state
    localStorage.removeItem('directus_token')
    setUser(null)
    
    // Force a hard navigation to login
    window.location.href = '/login'
  }

  const value = {
    user,
    loading,
    login,
    logout,
    error
  }

  return React.createElement(AuthContext.Provider, { value }, children)
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
} 