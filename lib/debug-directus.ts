import directus from './directus'
import { readItems } from '@directus/sdk'

/**
 * Debug utility for Directus integration
 */
export async function debugDirectus() {
  console.log('========== DIRECTUS DEBUG ==========')
  
  // Check environment variables
  console.log('Environment variables:')
  console.log(`DIRECTUS_URL: ${process.env.DIRECTUS_URL ? 'Set ✅' : 'Missing ❌'}`)
  console.log(`DIRECTUS_TOKEN: ${process.env.DIRECTUS_TOKEN ? 'Set ✅' : 'Missing ❌'}`)
  
  // Test connection
  try {
    console.log('\nTesting connection to Directus...')
    const result = await directus.request(
      readItems('ProdukGudang', { limit: 0 })
    )
    
    console.log('Connection successful ✅')
    console.log('Sample response:', result)
  } catch (error) {
    console.error('Connection failed ❌')
    console.error('Error details:', error)
  }
  
  // Test authentication (if token is set)
  if (process.env.DIRECTUS_TOKEN) {
    try {
      console.log('\nTesting authentication...')
      // Use a known collection instead of directus_users to test auth
      const response = await directus.request(
        readItems('ProdukGudang', { limit: 1 })
      )
      
      console.log('Authentication successful ✅')
      console.log('Authenticated request successful:', Boolean(response))
    } catch (error) {
      console.error('Authentication failed ❌')
      console.error('Error details:', error)
    }
  }
  
  // Try listing available collections through sample queries
  try {
    console.log('\nTesting available collections...')
    
    const collections = [
      'ProdukGudang',
      'StockMovement',
      'CurrentStock',
      'produk',
      'daily_production'
    ]
    
    for (const collection of collections) {
      try {
        await directus.request(
          readItems(collection as any, { limit: 1 })
        )
        console.log(`- ${collection}: Available ✅`)
      } catch (error) {
        console.log(`- ${collection}: Not available or no access ❌`)
      }
    }
  } catch (error) {
    console.error('Failed to test collections ❌')
    console.error('Error details:', error)
  }
  
  console.log('\n========== DEBUG COMPLETE ==========')
  return 'Debug complete'
}

/**
 * Test a specific collection query
 */
export async function testCollection(collectionName: string) {
  try {
    console.log(`Testing collection: ${collectionName}`)
    const items = await directus.request(
      readItems(collectionName as any, { limit: 1 })
    )
    
    console.log(`Collection ${collectionName} query successful ✅`)
    console.log('First item:', Array.isArray(items) && items.length > 0 ? items[0] : 'No items found')
    return items
  } catch (error) {
    console.error(`Collection ${collectionName} query failed ❌`)
    console.error('Error details:', error)
    throw error
  }
}

export default { debugDirectus, testCollection } 