import { createDirectus, rest, readItems, createItem, updateItem, deleteItem, authentication } from "@directus/sdk"

// Define types based on your Directus collections
export interface ProdukGudang {
  id?: string
  status?: string
  sort?: number | null
  date_created?: string
  user_updated?: string
  date_updated?: string
  name: string
  category: string
  description?: string
  sku: string
  unit: string // base unit
  purchase_price?: number // harga awal pembelian
  min_stock: number
  conversion_units?: string[] // array of conversion unit IDs
  created_at?: string
  updated_at?: string
}

export interface StockMovement {
  id?: string
  produk_gudang_id: string
  date: string
  type: "incoming" | "outgoing"
  quantity: number // always stored in base unit
  unit_used?: string // unit yang digunakan saat input
  quantity_converted?: number // quantity dalam unit yang digunakan
  base_unit_label?: string // nama unit untuk itemUnitName
  vendor_id?: string // M2O relation to vendor
  purchase_price?: number // harga pembelian per unit (dalam unit yang digunakan)
  total_cost?: number // total biaya (purchase_price * quantity_converted)
  reference_number?: string
  notes?: string
  created_by?: string
  created_at?: string
  // Accurate sync fields
  synced?: boolean
  accurate_id?: string
  synced_at?: string
  sync_error?: string
}

// Add new interfaces for production stock tracking
export interface ProductionStock {
  id: string
  produk: string
  quantity: number
  last_updated: string
}

export interface ProductionStockMovement {
  id?: string
  produk: string
  quantity: number
  type: "incoming" | "outgoing"
  date: string
  reference?: string
  notes?: string
}

export type CurrentStock = {
  id: string
  produk_gudang_id: string
  total_incoming: number
  total_outgoing: number
  current_stock: number
  last_updated: string
}

// Add new interface for daily production
export interface DailyProduction {
  id: string
  produk_id: string
  date: string
  quantity_produced: number
  target_quantity: number
  notes?: string
  created_at: string
  updated_at: string
}
export interface Produk {
  id: string
  status: string
  sort: number | null
  user_created: string
  date_created: string
  user_updated: string
  date_updated: string
  nama_produk: string
  foto: string
  diskripsi: string
  harga: number
  id_ketegori: number
  is_paket: boolean
  order_menu: number
}
// Define the schema for your Directus instance
interface Schema {
  ProdukGudang: ProdukGudang[]
  StockMovement: StockMovement[]
  CurrentStock: CurrentStock[]
  produk: Produk[] // Updated to use Produk interface
  DailyProduction: (DailyProduction & { produk: any })[]
  kangider: KangIder[]
  sales_planned: SalesPlanned[]
  sales_planned_items: SalesPlannedItem[]
  kas_harian: KasHarian[]
  stock_harian: StockHarian[]
  production_stock: ProductionStock[]
  stock_movement: ProductionStockMovement[]
  ProdukGudangConversion: ProdukGudangConversion[]
  Vendor: Vendor[]
  produk_gudang_conversion: ProdukGudangConversion[]
  vendor: Vendor[]
}

// Environment variables for both client and server
const DIRECTUS_URL = process.env.NEXT_PUBLIC_DIRECTUS_URL || process.env.DIRECTUS_URL

// Check for missing environment variables
if (!DIRECTUS_URL) {
  if (typeof window !== 'undefined') {
    console.error('Missing required environment variable: DIRECTUS_URL')
    console.error('Please create a .env.local file with NEXT_PUBLIC_DIRECTUS_URL')
  }
  throw new Error('Missing required environment variable: DIRECTUS_URL\nPlease create a .env.local file with this variable.')
}

// Create the Directus client instance with authentication
const directus = createDirectus<Schema>(DIRECTUS_URL).with(authentication()).with(rest())

// Initialize with token from localStorage if in browser or environment variable
let token = null;

// First try to get token from localStorage (client-side)
if (typeof window !== 'undefined') {
  token = localStorage.getItem('directus_token')
}

// If no token in localStorage, use the static token from environment variable
if (!token) {
  token = process.env.DIRECTUS_TOKEN || null
}

// Set token if available
if (token) {
  directus.setToken(token)
}

export default directus

// Helper functions for common operations
export async function getProducts() {
  try {
    // Get token from localStorage in browser environment or use static token
    let token = null;

    // First try to get token from localStorage (client-side)
    if (typeof window !== 'undefined') {
      token = localStorage.getItem('directus_token')
    }

    // If no token in localStorage, use the static token from environment variable
    if (!token) {
      token = process.env.DIRECTUS_TOKEN || null
    }

    // If still no token, throw error
    if (!token) {
      throw new Error('Authentication token not found')
    }

    // Set token in directus client
    directus.setToken(token)

    return await directus.request(readItems("ProdukGudang"))
  } catch (error) {
    console.error("Error fetching products:", error)
    return []
  }
}

export async function getProduct(id: string) {
  return await directus.request(
    readItems("ProdukGudang", {
      filter: { id: { _eq: id } },
    }),
  )
}

export async function createProduct(product: ProdukGudang) {
  return await directus.request(createItem("ProdukGudang", product))
}

export async function updateProduct(id: string, product: Partial<ProdukGudang>) {
  return await directus.request(updateItem("ProdukGudang", id, product))
}

export async function deleteProduct(id: string) {
  return await directus.request(deleteItem("ProdukGudang", id))
}

export async function getStockMovements(filters?: any) {
  try {
    const filterObject = filters ? {
      filter: filters
    } : {}

    return await directus.request(
      readItems("StockMovement", {
        sort: ["-date"],
        ...filterObject
      })
    )
  } catch (error) {
    console.error("Error in getStockMovements:", error)
    throw error
  }
}

export async function createStockMovement(movement: StockMovement) {
  try {
    // Get token from localStorage in browser environment or use static token
    let token = null;

    // First try to get token from localStorage (client-side)
    if (typeof window !== 'undefined') {
      token = localStorage.getItem('directus_token')
    }

    // If no token in localStorage, use the static token from environment variable
    if (!token) {
      token = process.env.DIRECTUS_TOKEN || null
    }

    // If still no token, throw error
    if (!token) {
      throw new Error('Authentication token not found')
    }

    // Set token in directus client
    directus.setToken(token)

    // Make a direct fetch call with explicit headers instead of using the SDK
    const response = await fetch(`${DIRECTUS_URL}/items/StockMovement`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(movement),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('API Error Response:', errorData);
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error creating stock movement:", error)
    throw error
  }
}

export async function getCurrentStock() {
  return await directus.request(readItems("CurrentStock"))
}

export async function getProductWithCurrentStock(productId: string) {
  const [product] = await directus.request(
    readItems("ProdukGudang", {
      filter: { id: { _eq: productId } },
    }),
  )

  const [stock] = await directus.request(
    readItems("CurrentStock", {
      filter: { produk_gudang_id: { _eq: productId } },
    }),
  )

  return { ...product, currentStock: stock?.current_stock || 0 }
}

// New helper functions for coffee products
export async function getCoffeeProducts() {
  try {
    // Use a more explicit approach with error handling
    const response = await directus.request(
      readItems("produk", {
        limit: -1, // Get all items
        fields: ["*"], // Get all fields
      }),
    )

    // Ensure we return an array
    return Array.isArray(response) ? response : []
  } catch (error) {
    console.error("Error in getCoffeeProducts:", error)
    return []
  }
}

export async function getCoffeeProduct(id: string) {
  return await directus.request(
    readItems("produk", {
      filter: { id: { _eq: id } },
    }),
  )
}

// New helper functions for daily production
export async function getDailyProduction(filters?: any) {
  try {
    const filterObject = filters ? {
      filter: filters
    } : {}

    return await directus.request(
      readItems("DailyProduction", {
        sort: ["-date"],
        ...filterObject
      })
    )
  } catch (error) {
    console.error("Error in getDailyProduction:", error)
    throw error
  }
}

export async function createDailyProduction(production: Omit<DailyProduction, "id" | "created_at" | "updated_at">) {
  try {
    return await directus.request(
      createItem("DailyProduction", {
        ...production,
        date: new Date().toISOString().split('T')[0]
      })
    )
  } catch (error) {
    console.error("Error in createDailyProduction:", error)
    throw error
  }
}

export async function updateDailyProduction(id: string, production: Partial<DailyProduction>) {
  try {
    return await directus.request(
      updateItem("DailyProduction", id, production)
    )
  } catch (error) {
    console.error("Error in updateDailyProduction:", error)
    throw error
  }
}

export async function deleteDailyProduction(id: string) {
  try {
    return await directus.request(
      deleteItem("DailyProduction", id)
    )
  } catch (error) {
    console.error("Error in deleteDailyProduction:", error)
    throw error
  }
}

// Get production data with product details
export async function getProductionWithDetails(filters?: any) {
  try {
    const productions = await directus.request(
      readItems("DailyProduction", {
        filter: filters,
        sort: ["-date"],
        fields: ["*", { produk: ["*"] }],
      }),
    )

    // Ensure we return an array
    return Array.isArray(productions) ? productions : []
  } catch (error) {
    console.error("Error in getProductionWithDetails:", error)
    return []
  }
}

// Get production summary by date range
export async function getProductionSummary(startDate: string, endDate: string) {
  try {
    const productions = await directus.request(
      readItems("DailyProduction", {
        filter: {
          date: {
            _eq: startDate
          }
        },
        fields: ["*", { produk: ["*"] }]
      })
    )

    // Group by product and calculate totals
    const summary = productions.reduce((acc: any, prod: any) => {
      const productId = prod.produk_id
      const nama_produk = prod.produk?.nama_produk
      if (!acc[productId]) {
        acc[productId] = {
          product: prod.produk || { id: productId, nama_produk },
          totalProduced: 0,
          days: 0
        }
      }
      acc[productId].totalProduced += prod.quantity_produced
      acc[productId].days += 1
      return acc
    }, {})

    return Object.values(summary)
  } catch (error) {
    console.error("Error in getProductionSummary:", error)
    throw error
  }
}

export type Product = ProdukGudang

export async function updateCurrentStock(id: string, data: Partial<CurrentStock>) {
  return directus.request(
    updateItem("CurrentStock", id, data)
  )
}

export async function createCurrentStock(data: Omit<CurrentStock, "id">) {
  return directus.request(
    createItem("CurrentStock", data)
  )
}

export async function handleNewStockMovement(movement: StockMovement, skipSync: boolean = false) {
  try {
    // Get token from localStorage in browser environment or use static token
    let token = null;

    // First try to get token from localStorage (client-side)
    if (typeof window !== 'undefined') {
      token = localStorage.getItem('directus_token')
    }

    // If no token in localStorage, use the static token from environment variable
    if (!token) {
      token = process.env.DIRECTUS_TOKEN || null
    }

    // If still no token, throw error
    if (!token) {
      throw new Error('Authentication token not found')
    }

    // Set token in directus client
    directus.setToken(token)

    // First create the stock movement using direct fetch
    const createdMovement = await createStockMovement(movement)
    console.log("Created movement:", createdMovement)

    // Get all movements for this product using direct fetch
    const movementsResponse = await fetch(`${DIRECTUS_URL}/items/StockMovement?filter[produk_gudang_id][_eq]=${movement.produk_gudang_id}&sort=-date`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      }
    });

    if (!movementsResponse.ok) {
      const errorData = await movementsResponse.json();
      console.error('API Error Response:', errorData);
      throw new Error(`API error: ${movementsResponse.status} ${movementsResponse.statusText}`);
    }

    const movementsData = await movementsResponse.json();
    const movements = movementsData.data || [];

    console.log("All movements for product:", movements)

    // Calculate totals
    const totalIncoming = movements
      .filter((m: any) => m.type === 'incoming')
      .reduce((sum: number, m: any) => sum + m.quantity, 0)

    const totalOutgoing = movements
      .filter((m: any) => m.type === 'outgoing')
      .reduce((sum: number, m: any) => sum + m.quantity, 0)

    const currentStock = totalIncoming - totalOutgoing
    console.log("Calculated totals:", { totalIncoming, totalOutgoing, currentStock })

    // Check if product exists in CurrentStock using direct fetch
    const existingResponse = await fetch(`${DIRECTUS_URL}/items/CurrentStock?filter[produk_gudang_id][_eq]=${movement.produk_gudang_id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      }
    });

    if (!existingResponse.ok) {
      const errorData = await existingResponse.json();
      console.error('API Error Response:', errorData);
      throw new Error(`API error: ${existingResponse.status} ${existingResponse.statusText}`);
    }

    const existingData = await existingResponse.json();
    const existing = existingData.data || [];

    console.log("Existing current stock:", existing)

    if (existing.length > 0) {
      // Update existing record using direct fetch
      const updateResponse = await fetch(`${DIRECTUS_URL}/items/CurrentStock/${existing[0].id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          total_incoming: totalIncoming,
          total_outgoing: totalOutgoing,
          current_stock: currentStock,
          last_updated: new Date().toISOString()
        })
      });

      if (!updateResponse.ok) {
        const errorData = await updateResponse.json();
        console.error('API Error Response:', errorData);
        throw new Error(`API error: ${updateResponse.status} ${updateResponse.statusText}`);
      }

      const updatedStock = await updateResponse.json();
      console.log("Updated current stock:", updatedStock)
    } else {
      // Create new record using direct fetch
      const createResponse = await fetch(`${DIRECTUS_URL}/items/CurrentStock`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          produk_gudang_id: movement.produk_gudang_id,
          total_incoming: totalIncoming,
          total_outgoing: totalOutgoing,
          current_stock: currentStock,
          last_updated: new Date().toISOString()
        })
      });

      if (!createResponse.ok) {
        const errorData = await createResponse.json();
        console.error('API Error Response:', errorData);
        throw new Error(`API error: ${createResponse.status} ${createResponse.statusText}`);
      }

      const newStock = await createResponse.json();
      console.log("Created new current stock:", newStock)
    }

    // Trigger Accurate sync webhook (fire and forget) - only if not skipped
    console.log("🔍 Debug skipSync value:", { skipSync, shouldSkip: !skipSync })
    if (!skipSync) {
      const movementId = createdMovement?.data?.id || createdMovement?.id
      console.log("🔍 Debug movement ID extraction:", {
        createdMovement: !!createdMovement,
        hasData: !!createdMovement?.data,
        dataId: createdMovement?.data?.id,
        directId: createdMovement?.id,
        extractedId: movementId
      })

      if (movementId) {
        console.log(`✅ Triggering sync for movement ID: ${movementId}`)
        triggerAccurateSync(movementId).catch((error: any) => {
          console.error("Failed to trigger Accurate sync:", error)
        })
      } else {
        console.warn("⚠️ Stock movement created but no ID returned, skipping sync")
        console.log("Created movement structure:", JSON.stringify(createdMovement, null, 2))
      }
    } else {
      console.log("⏭️ Skipping individual sync (bulk mode)")
    }

    return { success: true }
  } catch (error) {
    console.error("Error in handleNewStockMovement:", {
      error,
      errorMessage: error instanceof Error ? error.message : 'Unknown error',
      errorStack: error instanceof Error ? error.stack : undefined,
      movement
    })
    throw error
  }
}

export async function getProduk() {
  try {
    return await directus.request(
      readItems("produk", {
        sort: ["order_menu"],
        filter: {
          status: {
            _eq: "published"
          }
        }
      })
    )
  } catch (error) {
    console.error("Error in getProduk:", error)
    throw error
  }
}

// Add KangIder interface
export interface KangIder {
  id: string
  status: string
  nama: string
  notelp: string
  norekening: string
  nama_bank: string
  iduser: string[]
}

// Add SalesPlannedItem interface
export interface SalesPlannedItem {
  id?: string
  sales_planned_id?: string
  produk: string
  quantity: number
}

// Add SalesPlanned interface
export interface SalesPlanned {
  id: string
  kangider: string
  date: string
  status: "draft" | "published" | "load ider"
  total_cup: number
  items?: SalesPlannedItem[]
}

// KasHarian interface
export interface KasHarian {
  id: string;                    // UUID
  status: string;
  user_created?: string;         // UUID (User ID)
  date_created?: string;         // ISO timestamp
  user_updated?: string;         // UUID (User ID)
  date_updated?: string;         // ISO timestamp
  tanggal: string;               // YYYY-MM-DD
  waktu_buka?: string;           // HH:mm:ss
  waktu_tutup?: string;          // HH:mm:ss
  stok_awal?: number;
  stock_akhir?: number;
  petty_cash?: number;
  total_penjualan?: number;
  stock_produk?: any;            // JSON atau array produk
  keterangan?: string;
  idkangider: string;            // UUID (relasi ke KangIder)

  // Relasi opsional (jika ingin eager loading)
  kangider?: KangIder;
  stock_harian?: StockHarian[];  // One-to-many
}

// StockHarian interface
export interface StockHarian {
  id: string;                    // UUID
  id_produk: string;            // UUID (relasi ke produk)
  id_kangider: string;          // UUID (relasi ke KangIder)
  tanggal_stock: string;        // YYYY-MM-DD
  stock_awal: number;
  stock_akhir: number;
  status: string;
  user_created?: string;         // UUID
  date_created?: string;         // ISO timestamp
  user_updated?: string;         // UUID
  date_updated?: string;         // ISO timestamp
  kas_harian: string;            // UUID (relasi ke KasHarian)

  // Relasi opsional
  kangider?: KangIder;
  kasHarian?: KasHarian;
}

// Add helper functions for sales planning
export async function getKangIders() {
  try {
    return await directus.request(
      readItems('kangider', {
        filter: {
          status: {
            _eq: 'published'
          }
        }
      })
    )
  } catch (error) {
    console.error('Error in getKangIders:', error)
    throw error
  }
}

export async function getSalesPlans(filter?: { id?: { _eq: string }, date?: { _eq: string } }) {
  const response = await directus.request(
    readItems("sales_planned", {
      filter,
      fields: ["*", { items: ["*"] }]
    })
  );

  // Transform the response to ensure items is never null
  return response.map(plan => ({
    ...plan,
    items: plan.items || undefined
  })) as SalesPlanned[];
}

export async function createSalesPlan(data: Omit<SalesPlanned, "id">) {
  try {
    console.log("Creating sales plan with data:", data);

    // Create the sales plan with items included in the initial creation
    const result = await directus.request(
      createItem("sales_planned", {
        kangider: data.kangider,
        date: data.date,
        status: data.status,
        total_cup: data.total_cup,
        // Include the items relationship directly
        items: data.items?.map(item => ({
          produk: item.produk,
          quantity: item.quantity
        }))
      })
    );

    console.log("Sales plan created:", result);
    return result;
  } catch (error) {
    console.error("Error in createSalesPlan:", error);
    throw error;
  }
}

export async function updateSalesPlan(id: string, data: Partial<SalesPlanned>) {
  try {
    console.log("Updating sales plan with data:", data);

    // If status is changed to "load ider", we need to reduce production stock
    const shouldReduceStock = data.status === "load ider";

    const result = await directus.request(
      updateItem("sales_planned", id, {
        kangider: data.kangider,
        date: data.date,
        status: data.status,
        total_cup: data.total_cup,
        // Update items relationship directly
        items: data.items?.map(item => ({
          produk: item.produk,
          quantity: item.quantity
        }))
      })
    );
    console.log("Sales plan updated:", result);

    // Reduce stock if status is "load ider"
    if (shouldReduceStock && data.items && data.items.length > 0) {
      await reduceProductionStock(data.items);
    }

    return result;
  } catch (error) {
    console.error("Error in updateSalesPlan:", error);
    throw error;
  }
}

// New function to reduce production stock
export async function reduceProductionStock(items: SalesPlannedItem[]) {
  try {
    console.log("Reducing production stock for items:", items);

    for (const item of items) {
      if (item.quantity <= 0) continue;

      // Get current production stock for this product
      const currentStock = await directus.request(
        readItems("production_stock", {
          filter: { produk: { _eq: item.produk } },
          limit: 1
        })
      );

      if (currentStock && currentStock.length > 0) {
        const stockItem = currentStock[0];
        const newQuantity = Math.max(0, stockItem.quantity - item.quantity);

        // Update stock
        await directus.request(
          updateItem("production_stock", stockItem.id, {
            quantity: newQuantity,
            last_updated: new Date().toISOString()
          })
        );

        // Record stock movement
        await directus.request(
          createItem("stock_movement", {
            produk: item.produk,
            quantity: item.quantity,
            type: "outgoing",
            date: new Date().toISOString(),
            reference: `Sales Plan (${item.sales_planned_id})`,
            notes: "Stock reduced for Kang Ider loading"
          })
        );

        console.log(`Stock for product ${item.produk} reduced by ${item.quantity}`);
      } else {
        console.log(`No stock found for product ${item.produk}`);
      }
    }

    console.log("Production stock reduction completed");
    return { success: true };
  } catch (error) {
    console.error("Error reducing production stock:", error);
    throw error;
  }
}

export async function deleteSalesPlan(id: string) {
  return await directus.request(deleteItem("sales_planned", id))
}

// Interface for unit conversion
export interface ProdukGudangConversion {
  id: string
  unit_name: string
  conversion_to_base: number // conversion factor to base unit
  id_produk_gudang: string
}

// Interface for vendor
export interface Vendor {
  id: string
  date_created?: string
  date_updated?: string | null
  vendor_code: string
  vendor_name: string
  tax_id?: string | null
  phone?: string | null
  stock_movement?: string[] // O2M relation to stock movements
}

// ===== VENDOR CRUD FUNCTIONS =====
export async function getVendors() {
  try {
    return await directus.request(readItems("vendor"))
  } catch (error) {
    console.error("Error fetching vendors:", error)
    return []
  }
}

export async function getVendor(id: string) {
  return await directus.request(
    readItems("vendor", {
      filter: { id: { _eq: id } },
    }),
  )
}

export async function createVendor(vendor: Omit<Vendor, "id" | "date_created" | "date_updated">) {
  return await directus.request(createItem("vendor", vendor))
}

export async function updateVendor(id: string, vendor: Partial<Vendor>) {
  return await directus.request(updateItem("vendor", id, vendor))
}

export async function deleteVendor(id: string) {
  return await directus.request(deleteItem("vendor", id))
}

// ===== PRODUK GUDANG CONVERSION CRUD FUNCTIONS =====
export async function getConversionUnits(produkGudangId: string) {
  try {
    return await directus.request(
      readItems("produk_gudang_conversion", {
        filter: { id_produk_gudang: { _eq: produkGudangId } },
      })
    )
  } catch (error) {
    console.error("Error fetching conversion units:", error)
    return []
  }
}

export async function getAllConversionUnits() {
  try {
    return await directus.request(readItems("produk_gudang_conversion"))
  } catch (error) {
    console.error("Error fetching all conversion units:", error)
    return []
  }
}

export async function createConversionUnit(conversion: Omit<ProdukGudangConversion, "id">) {
  return await directus.request(createItem("produk_gudang_conversion", conversion))
}

export async function updateConversionUnit(id: string, conversion: Partial<ProdukGudangConversion>) {
  return await directus.request(updateItem("produk_gudang_conversion", id, conversion))
}

export async function deleteConversionUnit(id: string) {
  return await directus.request(deleteItem("produk_gudang_conversion", id))
}

// ===== UNIT CONVERSION UTILITY FUNCTIONS =====
export async function convertToBaseUnit(
  quantity: number,
  unitId: string,
  produkGudangId: string
): Promise<number> {
  try {
    // If unitId is empty, null, or "base", assume it's already base unit
    if (!unitId || unitId === "base") return quantity

    // Get conversion unit data
    const conversions = await getConversionUnits(produkGudangId)
    const conversion = conversions.find(c => c.id === unitId)

    if (!conversion) {
      console.warn(`Conversion unit ${unitId} not found, using quantity as-is`)
      return quantity
    }

    // Convert to base unit: quantity * conversion_to_base
    return quantity * conversion.conversion_to_base
  } catch (error) {
    console.error("Error converting to base unit:", error)
    return quantity
  }
}

export async function convertFromBaseUnit(
  baseQuantity: number,
  unitId: string,
  produkGudangId: string
): Promise<number> {
  try {
    // If unitId is empty, null, or "base", return base quantity
    if (!unitId || unitId === "base") return baseQuantity

    // Get conversion unit data
    const conversions = await getConversionUnits(produkGudangId)
    const conversion = conversions.find(c => c.id === unitId)

    if (!conversion) {
      console.warn(`Conversion unit ${unitId} not found, using base quantity`)
      return baseQuantity
    }

    // Convert from base unit: baseQuantity / conversion_to_base
    return baseQuantity / conversion.conversion_to_base
  } catch (error) {
    console.error("Error converting from base unit:", error)
    return baseQuantity
  }
}

export async function getAvailableUnitsForProduct(produkGudangId: string) {
  try {
    // Get product to get base unit
    const [product] = await getProduct(produkGudangId)
    if (!product) return []

    // Get conversion units
    const conversions = await getConversionUnits(produkGudangId)

    // Return base unit + conversion units
    const units = [
      {
        id: 'base', // "base" ID for base unit
        unit_name: product.unit,
        conversion_to_base: 1,
        is_base: true
      },
      ...conversions.map(c => ({
        ...c,
        is_base: false
      }))
    ]

    return units
  } catch (error) {
    console.error("Error getting available units:", error)
    return []
  }
}

// ===== ENHANCED STOCK MOVEMENT WITH CONVERSION =====
export async function createStockMovementWithConversion(movement: {
  produk_gudang_id: string
  date: string
  type: "incoming" | "outgoing"
  quantity: number
  unit_used?: string // ID of conversion unit, empty for base unit
  vendor_id?: string
  purchase_price?: number // harga per unit dalam unit yang digunakan
  reference_number?: string
  notes?: string
  created_by?: string
}, skipSync: boolean = false) {
  try {
    // Convert quantity to base unit if needed
    const baseQuantity = await convertToBaseUnit(
      movement.quantity,
      movement.unit_used || 'base',
      movement.produk_gudang_id
    )

    // Get unit name for base_unit_label
    const availableUnits = await getAvailableUnitsForProduct(movement.produk_gudang_id)
    const selectedUnit = availableUnits.find(u => u.id === movement.unit_used)
    const baseUnitLabel = selectedUnit ? selectedUnit.unit_name : undefined

    // Calculate total cost if purchase_price is provided
    const totalCost = movement.purchase_price ? movement.purchase_price * movement.quantity : undefined

    // Create stock movement with both original and converted quantities
    const stockMovement: StockMovement = {
      produk_gudang_id: movement.produk_gudang_id,
      date: movement.date,
      type: movement.type,
      quantity: baseQuantity, // stored in base unit
      unit_used: movement.unit_used === "base" ? undefined : movement.unit_used,
      quantity_converted: movement.quantity, // original quantity in selected unit
      base_unit_label: baseUnitLabel, // nama unit untuk itemUnitName
      vendor_id: movement.vendor_id === "none" ? undefined : movement.vendor_id,
      purchase_price: movement.purchase_price, // harga per unit dalam unit yang digunakan
      total_cost: totalCost, // total biaya
      reference_number: movement.reference_number,
      notes: movement.notes,
      created_by: movement.created_by,
      // Sync fields - default to not synced
      synced: false,
      accurate_id: undefined,
      synced_at: undefined,
      sync_error: undefined
    }

    // Debug logging
    console.log("🔍 Debug stockMovement object before save:", {
      quantity_converted: stockMovement.quantity_converted,
      base_unit_label: stockMovement.base_unit_label,
      unit_used: stockMovement.unit_used,
      baseUnitLabel,
      selectedUnit
    })

    // Use the existing handleNewStockMovement function
    return await handleNewStockMovement(stockMovement, skipSync)
  } catch (error) {
    console.error("Error creating stock movement with conversion:", error)
    throw error
  }
}

// ===== ACCURATE SYNC TRIGGER =====
export async function triggerAccurateSync(stockMovementId: string) {
  try {
    console.log('🔄 Triggering Accurate sync for movement:', stockMovementId)

    // Check if we're in server-side context
    if (typeof window !== 'undefined') {
      console.log('⚠️ Client-side context detected, skipping direct sync')
      return { success: false, error: 'Sync can only be triggered from server-side' }
    }

    // Ensure we have Directus token
    if (!process.env.DIRECTUS_TOKEN) {
      console.error('❌ DIRECTUS_TOKEN not found in environment')
      return { success: false, error: 'DIRECTUS_TOKEN not configured' }
    }

    // Import the sync service directly
    const { accurateSync } = await import('./accurate-sync')

    // Call sync directly
    const result = await accurateSync.syncStockMovement(stockMovementId)

    console.log('✅ Accurate sync result:', result)
    return result
  } catch (error) {
    console.error('❌ Failed to trigger Accurate sync:', error)
    // Don't throw error to prevent stock movement creation from failing
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

// ===== KAS HARIAN FUNCTIONS =====
export async function getKasHarian(filters?: any) {
  try {
    const filterObject = filters ? { filter: filters } : {}

    return await directus.request(
      readItems("kas_harian", {
        sort: ["-tanggal"],
        fields: ["*", { kangider: ["*"] }, { stock_harian: ["*"] }],
        ...filterObject
      })
    )
  } catch (error) {
    console.error("Error in getKasHarian:", error)
    throw error
  }
}

export async function createKasHarian(kasHarian: Omit<KasHarian, "id" | "date_created" | "date_updated">) {
  try {
    return await directus.request(
      createItem("kas_harian", {
        ...kasHarian,
        date_created: new Date().toISOString()
      })
    )
  } catch (error) {
    console.error("Error in createKasHarian:", error)
    throw error
  }
}

export async function updateKasHarian(id: string, kasHarian: Partial<KasHarian>) {
  try {
    return await directus.request(
      updateItem("kas_harian", id, {
        ...kasHarian,
        date_updated: new Date().toISOString()
      })
    )
  } catch (error) {
    console.error("Error in updateKasHarian:", error)
    throw error
  }
}

// ===== STOCK HARIAN FUNCTIONS =====
export async function getStockHarian(filters?: any) {
  try {
    const filterObject = filters ? { filter: filters } : {}

    return await directus.request(
      readItems("stock_harian", {
        sort: ["-tanggal_stock"],
        fields: ["*", { kangider: ["*"] }, { kasHarian: ["*"] }],
        ...filterObject
      })
    )
  } catch (error) {
    console.error("Error in getStockHarian:", error)
    throw error
  }
}

export async function createStockHarian(stockHarian: Omit<StockHarian, "id" | "date_created">) {
  try {
    return await directus.request(
      createItem("stock_harian", {
        ...stockHarian,
        date_created: new Date().toISOString()
      })
    )
  } catch (error) {
    console.error("Error in createStockHarian:", error)
    throw error
  }
}

export async function updateStockHarian(id: string, stockHarian: Partial<StockHarian>) {
  try {
    return await directus.request(
      updateItem("stock_harian", id, {
        ...stockHarian,
        date_updated: new Date().toISOString()
      })
    )
  } catch (error) {
    console.error("Error in updateStockHarian:", error)
    throw error
  }
}

// ===== ENHANCED SALES PLAN FUNCTIONS FOR KANG IDER WORKFLOW =====

// Function to process "load ider" status and create kas_harian + stock_harian
export async function processSalesPlanLoadIder(salesPlanId: string) {
  try {
    console.log("Processing sales plan for load ider:", salesPlanId)

    // Get sales plan with items
    const salesPlans = await getSalesPlans({ id: { _eq: salesPlanId } })
    if (!salesPlans || salesPlans.length === 0) {
      throw new Error("Sales plan not found")
    }

    const salesPlan = salesPlans[0]
    if (!salesPlan.items || salesPlan.items.length === 0) {
      throw new Error("Sales plan has no items")
    }

    // Create kas_harian record
    const kasHarian = await createKasHarian({
      status: "published",
      tanggal: salesPlan.date,
      idkangider: salesPlan.kangider,
      stok_awal: salesPlan.total_cup,
      stock_akhir: salesPlan.total_cup, // Initially same as stok_awal
      keterangan: `Created from Sales Plan ${salesPlanId}`
    })

    console.log("Created kas_harian:", kasHarian)

    // Create stock_harian records for each item
    const stockHarianRecords = []
    for (const item of salesPlan.items) {
      const stockHarian = await createStockHarian({
        id_produk: item.produk,
        id_kangider: salesPlan.kangider,
        tanggal_stock: salesPlan.date,
        stock_awal: item.quantity,
        stock_akhir: item.quantity, // Initially same as stock_awal
        status: "published",
        kas_harian: kasHarian.id
      })
      stockHarianRecords.push(stockHarian)
    }

    console.log("Created stock_harian records:", stockHarianRecords)

    // Reduce production stock (existing function)
    await reduceProductionStock(salesPlan.items)

    return {
      kasHarian,
      stockHarianRecords,
      message: "Sales plan processed successfully for load ider"
    }
  } catch (error) {
    console.error("Error processing sales plan load ider:", error)
    throw error
  }
}

// Enhanced update function that handles the workflow
export async function updateSalesPlanWithWorkflow(id: string, data: Partial<SalesPlanned>) {
  try {
    console.log("Updating sales plan with workflow:", id, data)

    // Check if status is changing to "load ider"
    const shouldProcessLoadIder = data.status === "load ider"

    // Update the sales plan first
    const result = await updateSalesPlan(id, data)

    // If status changed to "load ider", process the workflow
    if (shouldProcessLoadIder) {
      const workflowResult = await processSalesPlanLoadIder(id)
      return {
        salesPlan: result,
        workflow: workflowResult
      }
    }

    return { salesPlan: result }
  } catch (error) {
    console.error("Error in updateSalesPlanWithWorkflow:", error)
    throw error
  }
}

