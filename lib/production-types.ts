// Enhanced Production Management Types

export interface ProductionStock {
  id: string
  produk_id: string
  quantity: number
  reserved_quantity: number // quantity yang sudah dialokasikan untuk distribusi
  available_quantity: number // quantity yang tersedia untuk distribusi
  last_updated: string
  created_at: string
}

export interface DistributionChannel {
  id: string
  name: string
  type: 'kangider' | 'sekolah' | 'reseller'
  contact_person?: string
  phone?: string
  address?: string
  status: 'active' | 'inactive'
  created_at: string
}

export interface ProductionDistribution {
  id: string
  produk_id: string
  distribution_channel_id: string
  quantity: number
  date: string
  status: 'planned' | 'distributed' | 'returned' | 'completed'
  notes?: string
  distributed_by?: string
  distributed_at?: string
  returned_quantity?: number
  returned_at?: string
  return_reason?: string
  created_at: string
  updated_at: string
}

export interface ProductionReturn {
  id: string
  distribution_id: string
  produk_id: string
  quantity: number
  reason: string
  condition: 'good' | 'damaged' | 'expired'
  action: 'restock' | 'dispose' | 'redistribute'
  returned_by?: string
  returned_at: string
  processed_at?: string
  notes?: string
}

export interface DailyProductionSummary {
  date: string
  total_produced: number
  total_distributed: number
  total_returned: number
  total_available: number
  products: {
    produk_id: string
    nama_produk: string
    produced: number
    distributed: number
    returned: number
    available: number
    distributions: {
      channel_name: string
      channel_type: string
      quantity: number
      status: string
    }[]
  }[]
}

export interface ProductionAnalytics {
  period: 'daily' | 'weekly' | 'monthly'
  start_date: string
  end_date: string
  total_production: number
  total_distribution: number
  total_returns: number
  return_rate: number
  top_products: {
    produk_id: string
    nama_produk: string
    total_produced: number
    total_distributed: number
    distribution_rate: number
  }[]
  channel_performance: {
    channel_id: string
    channel_name: string
    channel_type: string
    total_distributed: number
    total_returned: number
    return_rate: number
    performance_score: number
  }[]
}

// Enhanced Daily Production with distribution tracking
export interface EnhancedDailyProduction {
  id: string
  produk_id: string
  date: string
  quantity_produced: number
  target_quantity: number
  quantity_distributed: number
  quantity_available: number
  quantity_returned: number
  notes?: string
  status: 'active' | 'completed' | 'archived'
  created_at: string
  updated_at: string
  
  // Relations
  produk?: {
    id: string
    nama_produk: string
    harga: number
  }
  distributions?: ProductionDistribution[]
  returns?: ProductionReturn[]
}

// Distribution Planning
export interface DistributionPlan {
  id: string
  date: string
  status: 'draft' | 'approved' | 'in_progress' | 'completed'
  total_items: number
  created_by: string
  approved_by?: string
  approved_at?: string
  completed_at?: string
  notes?: string
  
  items: DistributionPlanItem[]
}

export interface DistributionPlanItem {
  id: string
  distribution_plan_id: string
  produk_id: string
  distribution_channel_id: string
  planned_quantity: number
  actual_quantity?: number
  status: 'planned' | 'distributed' | 'partial' | 'cancelled'
  notes?: string
}

// API Response Types
export interface ProductionDashboardData {
  today: DailyProductionSummary
  week: ProductionAnalytics
  month: ProductionAnalytics
  alerts: {
    low_stock: { produk_id: string; nama_produk: string; quantity: number }[]
    high_returns: { channel_id: string; channel_name: string; return_rate: number }[]
    production_targets: { produk_id: string; nama_produk: string; target: number; actual: number; variance: number }[]
  }
}

export interface DistributionFormData {
  produk_id: string
  distributions: {
    channel_id: string
    quantity: number
    notes?: string
  }[]
}

export interface ReturnFormData {
  distribution_id: string
  quantity: number
  reason: string
  condition: 'good' | 'damaged' | 'expired'
  action: 'restock' | 'dispose' | 'redistribute'
  notes?: string
}
