# 🏗️ SYSTEM PATTERNS - WA<PERSON>HOUSE MANAGEMENT WITH ACCURATE INTEGRATION

## 🎯 ARCHITECTURAL PATTERNS OVERVIEW

This document captures the key architectural patterns, design decisions, and implementation strategies used in the Warehouse Management System with Accurate Online integration.

## 🔧 CORE ARCHITECTURAL PATTERNS

### 1. API-First Architecture ✅
```typescript
// Pattern: RESTful API with Next.js App Router
Pattern Type: Backend Architecture
Implementation: Next.js 15 API Routes

Structure:
/api/products/          - Product CRUD operations
/api/stock/            - Stock movement operations  
/api/accurate/         - OAuth and authentication
/api/sync/             - Synchronization endpoints

Benefits:
- Clean separation of concerns
- Easy testing and validation
- Third-party integration ready
- Scalable endpoint structure
```

### 2. Real-time Synchronization Pattern ✅
```typescript
// Pattern: Webhook-driven Event Synchronization
Pattern Type: Integration Architecture
Implementation: Real-time sync with retry fallback

Flow:
User Action → Database Save → Webhook Trigger → 
Accurate API Call → Status Update → Error Handling

Key Components:
- Immediate sync attempt
- Retry mechanism for failures
- Manual sync override
- Comprehensive error logging
```

### 3. Multi-unit Conversion Engine ✅
```typescript
// Pattern: Real-time Calculation Engine
Pattern Type: Business Logic Pattern
Implementation: Dynamic unit conversion system

Structure:
Base Unit Definition → Conversion Factors → 
Real-time Calculation → Display Updates

Features:
- Dynamic conversion factor lookup
- Real-time calculation updates
- Base unit label generation
- Quantity conversion tracking
```

## 🔄 INTEGRATION PATTERNS

### 1. OAuth 2.0 Authentication Flow ✅
```typescript
// Pattern: Secure API Integration
Integration: Accurate Online API
Security: OAuth 2.0 with token management

Flow:
User Request → OAuth Redirect → Authorization → 
Token Exchange → API Access → Token Refresh

Implementation:
- Secure token storage
- Automatic token refresh
- Error handling for auth failures
- Session management
```

### 2. Dual API Integration Pattern ✅
```typescript
// Pattern: Context-aware API Endpoint Selection
Implementation: Purchase Invoice vs Job Order

Logic:
Stock Movement Type → API Endpoint Selection → 
Payload Construction → Sync Execution

Incoming Stock → Purchase Invoice API
Outgoing Stock → Job Order API

Data Mapping:
- Common fields: quantity, price, unit
- Specific fields: vendor (incoming), customer (outgoing)
- Status management: draft vs final
```

### 3. Error Resilience Pattern ✅
```typescript
// Pattern: Comprehensive Error Handling
Implementation: Multi-layer error management

Layers:
1. Input Validation (Frontend + API)
2. Database Transaction Safety
3. API Call Error Handling
4. Retry Mechanism
5. Manual Override System

Recovery Strategy:
- Automatic retry with exponential backoff
- Manual sync interface for failed items
- Error logging and monitoring
- Data integrity preservation
```

## 🗄️ DATA PATTERNS

### 1. Normalized Database Schema ✅
```sql
-- Pattern: Relational Data Model with Clear Relationships
-- Implementation: PostgreSQL with Directus CMS

Core Tables:
- produk_gudang (Products): Master product data
- stock_movement (Movements): Transaction records
- produk_gudang_conversion (Units): Conversion factors
- vendor (Vendors): Vendor master data

Relationships:
- Products → Conversions (1:many)
- Products → Movements (1:many)  
- Vendors → Movements (1:many)
- Conversions → Movements (many:1 via unit_used)
```

### 2. Audit Trail Pattern ✅
```typescript
// Pattern: Complete Activity Tracking
Implementation: Comprehensive logging system

Tracked Data:
- All stock movements with timestamps
- Sync status and error messages
- User actions and changes
- API call results and responses

Benefits:
- Complete audit trail
- Error debugging capability
- Compliance requirements
- Data integrity verification
```

### 3. Calculated Field Pattern ✅
```typescript
// Pattern: Real-time Computed Values
Implementation: Dynamic calculation system

Calculated Fields:
- quantity_converted: Real-time unit conversion
- base_unit_label: Dynamic unit name resolution
- current_stock: Real-time stock level calculation
- sync_status: Dynamic status determination

Update Strategy:
- Calculate on form interaction
- Store calculated values for performance
- Validate calculations on save
- Recalculate on data changes
```

## 🎨 UI/UX PATTERNS

### 1. Progressive Enhancement Form Pattern ✅
```typescript
// Pattern: Smart Form with Auto-completion
Implementation: React Hook Form + Real-time validation

Features:
- Product auto-complete with search
- Unit dropdown with conversion preview
- Price auto-population from product data
- Real-time validation feedback
- Conditional field display (vendor for incoming)

User Experience:
- Minimal required inputs
- Smart defaults and suggestions
- Immediate feedback on errors
- Clear success/failure indicators
```

### 2. Dashboard Pattern ✅
```typescript
// Pattern: Real-time Monitoring Interface
Implementation: Stock overview with live updates

Components:
- Current stock levels (calculated)
- Recent movements (chronological)
- Sync status indicators (real-time)
- Error alerts and resolution

Data Flow:
- Real-time stock calculations
- Movement history with filtering
- Sync status monitoring
- Manual action triggers
```

### 3. Error State Management ✅
```typescript
// Pattern: User-friendly Error Handling
Implementation: Clear error communication

Error Types:
- Validation errors: Immediate inline feedback
- Sync errors: Clear status indicators
- Network errors: Retry suggestions
- System errors: Contact information

Recovery Actions:
- Clear error descriptions
- Suggested resolution steps
- Manual retry buttons
- Support contact options
```

## 🔒 SECURITY PATTERNS

### 1. Input Validation Pattern ✅
```typescript
// Pattern: Multi-layer Input Validation
Implementation: Client + Server validation

Validation Layers:
- Frontend: React Hook Form validation
- API: Request body validation
- Database: Schema constraints
- Business Logic: Custom validation rules

Validation Rules:
- Required field enforcement
- Data type validation
- Business rule validation
- SQL injection prevention
```

### 2. Secure API Communication ✅
```typescript
// Pattern: Protected API Endpoints
Implementation: Authentication and authorization

Security Measures:
- OAuth 2.0 for external APIs
- API key validation for webhooks
- Request rate limiting
- Input sanitization
- Error message sanitization
```

## 📊 PERFORMANCE PATTERNS

### 1. Efficient Database Queries ✅
```sql
-- Pattern: Optimized Query Structure
-- Implementation: Indexed queries with minimal joins

Query Optimization:
- Primary key indexes on all tables
- Foreign key indexes for relationships
- Composite indexes for common filters
- Paginated results for large datasets

Common Queries:
- Product lookup by SKU (indexed)
- Stock movements by product (indexed)
- Conversion factors by product (indexed)
- Current stock calculation (optimized)
```

### 2. Real-time Calculation Caching ✅
```typescript
// Pattern: Calculated Value Caching
Implementation: Store computed values for performance

Cached Calculations:
- quantity_converted: Stored with movement
- base_unit_label: Cached unit name
- current_stock: Calculated on demand
- sync_status: Updated on state change

Cache Strategy:
- Calculate once, store result
- Invalidate on data change
- Lazy loading for complex calculations
- Fallback to real-time calculation
```

## 🔄 DEPLOYMENT PATTERNS

### 1. Environment Configuration ✅
```typescript
// Pattern: Environment-specific Configuration
Implementation: Next.js environment variables

Configuration Areas:
- Database connections (Directus)
- API endpoints (Accurate Online)
- OAuth credentials (secure storage)
- Feature flags (environment-specific)

Security:
- Sensitive data in environment variables
- No hardcoded credentials
- Environment-specific configurations
- Secure secret management
```

### 2. Error Monitoring Pattern ✅
```typescript
// Pattern: Comprehensive Error Tracking
Implementation: Structured logging system

Monitoring Areas:
- API call failures
- Database errors
- Sync failures
- User interface errors

Logging Structure:
- Error categorization
- Context preservation
- Stack trace capture
- User action correlation
```

## 🎯 BUSINESS LOGIC PATTERNS

### 1. Stock Movement Business Rules ✅
```typescript
// Pattern: Business Rule Enforcement
Implementation: Centralized business logic

Business Rules:
- Incoming stock requires vendor
- Outgoing stock uses purchase price from product
- Unit conversions must use defined factors
- Sync status tracking for all movements

Enforcement:
- Database constraints
- API validation
- Frontend validation
- Business logic validation
```

### 2. Integration State Management ✅
```typescript
// Pattern: Sync State Tracking
Implementation: Comprehensive status management

Sync States:
- pending: Not yet synced
- synced: Successfully synced
- error: Sync failed
- manual: Requires manual intervention

State Transitions:
- Created → Pending
- Pending → Synced (success)
- Pending → Error (failure)
- Error → Pending (retry)
- Error → Manual (escalation)
```

---

*Document Type: System Patterns*  
*Last Updated: 2024-12-27*  
*Version: 1.0*  
*Classification: Internal* 