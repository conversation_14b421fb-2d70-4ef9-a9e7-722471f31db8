# 🔧 TECHNICAL CONTEXT - WAREHOUSE MANAGEMENT SYSTEM WITH ACCURATE INTEGRATION

## 📚 TECHNOLOGY STACK - PRODUCTION SYSTEM

### Frontend Technologies ✅
| Technology | Version | Purpose | Status |
|------------|---------|---------|---------|
| Next.js | 15.x | React framework with App Router | ✅ Production |
| React | 19.x | UI library | ✅ Production |
| TypeScript | 5.x | Type safety | ✅ Production |
| Tailwind CSS | 3.4.x | Styling framework | ✅ Production |
| shadcn/ui | Latest | UI component library | ✅ Production |
| React Hook Form | Latest | Form management | ✅ Production |
| Zod | Latest | Data validation | ✅ Production |
| OAuth 2.0 | - | Accurate Online authentication | ✅ Production |

### Backend & Infrastructure ✅
| Technology | Version | Purpose | Status |
|------------|---------|---------|---------|
| Next.js API Routes | 15.x | RESTful API endpoints | ✅ Production |
| Directus CMS | Latest | Headless CMS & Database management | ✅ Production |
| PostgreSQL | 14+ | Primary database | ✅ Production |
| Accurate Online API | v1 | Accounting system integration | ✅ Production |
| Webhook System | - | Real-time synchronization | ✅ Production |

### Integration Technologies ✅
| Technology | Version | Purpose | Status |
|------------|---------|---------|---------|
| OAuth 2.0 | 2.0 | Secure authentication with Accurate | ✅ Production |
| REST APIs | - | Data exchange and synchronization | ✅ Production |
| Webhooks | - | Real-time event handling | ✅ Production |
| JWT Tokens | - | Session management | ✅ Production |

## 🏗️ ARCHITECTURE IMPLEMENTATION - PRODUCTION READY

### Application Structure ✅
```
├── app/                           # Next.js App Router
│   ├── api/                      # API Routes
│   │   ├── products/            # Product CRUD operations
│   │   ├── stock/               # Stock movement APIs
│   │   ├── accurate/            # Accurate Online integration
│   │   │   ├── auth/           # OAuth 2.0 authentication
│   │   │   └── callback/       # OAuth callback handler
│   │   └── sync/               # Synchronization endpoints
│   │       ├── stock-hook/     # Real-time sync webhook
│   │       └── manual/         # Manual sync operations
│   ├── products/               # Product management pages
│   │   ├── page.tsx           # Product listing
│   │   ├── add/               # Add product form
│   │   └── [id]/              # Product detail/edit
│   ├── stock/                  # Stock management pages
│   │   ├── page.tsx           # Stock dashboard
│   │   └── add-movement/      # Stock movement form
│   └── sync/                   # Sync monitoring page
├── components/                  # Reusable UI components
│   ├── ui/                     # shadcn/ui base components
│   ├── forms/                  # Form components
│   ├── tables/                 # Data table components
│   └── sync/                   # Sync status components
├── lib/                        # Utility libraries
│   ├── directus.ts            # Directus client configuration
│   ├── accurate.ts            # Accurate Online API client
│   └── utils.ts               # Common utilities
└── types/                      # TypeScript definitions
    ├── product.ts             # Product type definitions
    ├── stock.ts               # Stock movement types
    └── accurate.ts            # Accurate API types
```

### Database Architecture ✅
```sql
-- Production Database Schema (PostgreSQL via Directus)

-- Core Product Management
TABLE produk_gudang {
    id UUID PRIMARY KEY
    name VARCHAR(255) NOT NULL
    category VARCHAR(100)
    sku VARCHAR(100) UNIQUE NOT NULL
    unit VARCHAR(50) NOT NULL
    purchase_price DECIMAL(10,2) NOT NULL
    min_stock INTEGER DEFAULT 0
    description TEXT
    created_at TIMESTAMP DEFAULT NOW()
    updated_at TIMESTAMP DEFAULT NOW()
}

-- Stock Movement Tracking
TABLE stock_movement {
    id UUID PRIMARY KEY
    type VARCHAR(10) NOT NULL CHECK (type IN ('incoming', 'outgoing'))
    quantity INTEGER NOT NULL
    quantity_converted INTEGER NOT NULL
    base_unit_label VARCHAR(50) NOT NULL
    purchase_price DECIMAL(10,2) NOT NULL
    reference_number VARCHAR(100)
    produk_gudang_id UUID REFERENCES produk_gudang(id)
    vendor_id INTEGER REFERENCES vendor(id)
    unit_used UUID REFERENCES produk_gudang_conversion(id)
    synced BOOLEAN DEFAULT FALSE
    accurate_id VARCHAR(100)
    accurate_no VARCHAR(100)
    sync_error TEXT
    created_at TIMESTAMP DEFAULT NOW()
    updated_at TIMESTAMP DEFAULT NOW()
}

-- Multi-unit Conversion System
TABLE produk_gudang_conversion {
    id UUID PRIMARY KEY
    produk_gudang_id UUID REFERENCES produk_gudang(id)
    unit_name VARCHAR(50) NOT NULL
    conversion_factor DECIMAL(10,4) NOT NULL
    is_base_unit BOOLEAN DEFAULT FALSE
    created_at TIMESTAMP DEFAULT NOW()
}

-- Vendor Management
TABLE vendor {
    id SERIAL PRIMARY KEY
    vendor_name VARCHAR(255) NOT NULL
    vendor_code VARCHAR(50) UNIQUE NOT NULL
    contact_info TEXT
    created_at TIMESTAMP DEFAULT NOW()
    updated_at TIMESTAMP DEFAULT NOW()
}
```

### Integration Flow Architecture ✅
```mermaid
graph TD
    UserForm[User Form Input] --> Validation[Form Validation]
    Validation --> Database[Database Save]
    Database --> Webhook[Webhook Trigger]
    Webhook --> AccurateAuth[Accurate OAuth Check]
    AccurateAuth --> APISelect{Stock Type?}
    
    APISelect -->|Incoming| PurchaseInv[Purchase Invoice API]
    APISelect -->|Outgoing| JobOrder[Job Order API]
    
    PurchaseInv --> SyncSuccess[Sync Success]
    JobOrder --> SyncSuccess
    
    PurchaseInv --> SyncError[Sync Error]
    JobOrder --> SyncError
    
    SyncError --> RetryMech[Retry Mechanism]
    RetryMech --> ManualSync[Manual Sync Interface]
    
    SyncSuccess --> StatusUpdate[Update Sync Status]
    ManualSync --> StatusUpdate
    
    style UserForm fill:#4da6ff,stroke:#0066cc,color:white
    style Database fill:#4dbb5f,stroke:#36873f,color:white
    style SyncSuccess fill:#8cff8c,stroke:#4dbb5f,color:black
    style SyncError fill:#ff8c8c,stroke:#cc4444,color:white
    style ManualSync fill:#ffa64d,stroke:#cc7a30,color:white
```

## 🔄 ACCURATE ONLINE INTEGRATION IMPLEMENTATION

### OAuth 2.0 Authentication Flow ✅
```typescript
// Production OAuth Implementation
const ACCURATE_OAUTH_CONFIG = {
  authorizationURL: 'https://account.accurate.id/oauth/authorize',
  tokenURL: 'https://account.accurate.id/oauth/token',
  clientId: process.env.ACCURATE_CLIENT_ID,
  clientSecret: process.env.ACCURATE_CLIENT_SECRET,
  redirectURI: process.env.ACCURATE_REDIRECT_URI,
  scope: 'view_item create_purchase_invoice create_job_order'
}

// Authentication Flow Implementation
1. User initiates sync → /api/accurate/auth
2. Redirect to Accurate OAuth → account.accurate.id
3. User authorizes application
4. Callback with auth code → /api/accurate/callback
5. Exchange code for access token
6. Store token securely for API calls
```

### API Integration Patterns ✅
```typescript
// Purchase Invoice (Incoming Stock)
const createPurchaseInvoice = async (movement: StockMovement) => {
  const payload = {
    'branch.id': '50',
    'vendor.id': movement.vendor.vendor_code,
    'transDate': formatDate(movement.created_at),
    'useAutoNumber': 'true',
    'detailItem[0].itemNo': movement.product.sku,
    'detailItem[0].quantity': movement.quantity_converted,
    'detailItem[0].unitPrice': movement.purchase_price,
    'detailItem[0].itemUnitName': movement.base_unit_label,
    'referenceNo': movement.reference_number
  }
  
  return await accurateAPI.post('/purchase-invoice.do', payload)
}

// Job Order (Outgoing Stock)
const createJobOrder = async (movement: StockMovement) => {
  const payload = {
    'branchId': '50',
    'customerNo': 'C.00001',
    'transDate': formatDate(movement.created_at),
    'useAutoNumber': 'true',
    'detailItem[0].itemNo': movement.product.sku,
    'detailItem[0].quantity': movement.quantity_converted,
    'detailItem[0].unitPrice': movement.purchase_price,
    'detailItem[0].itemUnitName': movement.base_unit_label,
    'saveAsStatusType': 'DRAFT'
  }
  
  return await accurateAPI.post('/job-order.do', payload)
}
```

## 🎯 BUSINESS LOGIC PATTERNS

### Multi-unit Conversion Engine ✅
```typescript
// Real-time Unit Conversion Implementation
const calculateConversion = (
  quantity: number,
  fromUnit: string,
  toBaseUnit: string,
  conversionFactor: number
) => {
  const quantityConverted = quantity * conversionFactor
  const baseUnitLabel = toBaseUnit
  
  return {
    quantity_converted: quantityConverted,
    base_unit_label: baseUnitLabel
  }
}

// Auto-population of Purchase Price
const getProductDefaults = async (productId: string) => {
  const product = await directus.items('produk_gudang').readOne(productId)
  return {
    purchase_price: product.purchase_price,
    unit: product.unit,
    conversions: await getProductConversions(productId)
  }
}
```

### Error Handling & Retry System ✅
```typescript
// Comprehensive Error Handling
const syncWithRetry = async (movement: StockMovement, maxRetries = 3) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await syncToAccurate(movement)
      await updateSyncStatus(movement.id, {
        synced: true,
        accurate_id: result.id,
        accurate_no: result.number,
        sync_error: null
      })
      return result
    } catch (error) {
      if (attempt === maxRetries) {
        await updateSyncStatus(movement.id, {
          synced: false,
          sync_error: error.message
        })
        throw error
      }
      await delay(Math.pow(2, attempt) * 1000) // Exponential backoff
    }
  }
}
```

## 🔒 SECURITY IMPLEMENTATION

### Authentication & Authorization ✅
```typescript
// OAuth Token Management
const tokenManager = {
  store: (tokens) => secureStorage.set('accurate_tokens', tokens),
  retrieve: () => secureStorage.get('accurate_tokens'),
  refresh: async (refreshToken) => {
    const response = await fetch(ACCURATE_OAUTH_CONFIG.tokenURL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({
        grant_type: 'refresh_token',
        refresh_token: refreshToken,
        client_id: ACCURATE_OAUTH_CONFIG.clientId,
        client_secret: ACCURATE_OAUTH_CONFIG.clientSecret
      })
    })
    return response.json()
  }
}
```

### Input Validation & Sanitization ✅
```typescript
// Zod Schema Validation
const StockMovementSchema = z.object({
  type: z.enum(['incoming', 'outgoing']),
  quantity: z.number().positive(),
  produk_gudang_id: z.string().uuid(),
  vendor_id: z.number().optional(),
  unit_used: z.string().uuid(),
  reference_number: z.string().optional()
})

// API Route Protection
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = StockMovementSchema.parse(body)
    // Process validated data
  } catch (error) {
    return NextResponse.json({ error: 'Invalid input' }, { status: 400 })
  }
}
```

## 📊 PERFORMANCE OPTIMIZATIONS

### Database Query Optimization ✅
```sql
-- Strategic Indexing
CREATE INDEX idx_stock_movement_product ON stock_movement(produk_gudang_id);
CREATE INDEX idx_stock_movement_sync ON stock_movement(synced);
CREATE INDEX idx_product_sku ON produk_gudang(sku);
CREATE INDEX idx_conversion_product ON produk_gudang_conversion(produk_gudang_id);

-- Optimized Current Stock Calculation
SELECT 
    p.id,
    p.name,
    p.sku,
    COALESCE(
        SUM(CASE WHEN sm.type = 'incoming' THEN sm.quantity_converted ELSE 0 END) -
        SUM(CASE WHEN sm.type = 'outgoing' THEN sm.quantity_converted ELSE 0 END),
        0
    ) as current_stock
FROM produk_gudang p
LEFT JOIN stock_movement sm ON p.id = sm.produk_gudang_id
GROUP BY p.id, p.name, p.sku;
```

### Frontend Performance ✅
```typescript
// Code Splitting for Pages
const ProductPage = dynamic(() => import('./ProductPage'), {
  loading: () => <ProductPageSkeleton />
})

// Optimized Form Performance
const StockMovementForm = () => {
  const form = useForm({
    resolver: zodResolver(StockMovementSchema),
    mode: 'onChange'
  })
  
  const productQuery = useQuery({
    queryKey: ['products'],
    queryFn: fetchProducts,
    staleTime: 5 * 60 * 1000 // 5 minutes
  })
  
  return <FormComponent />
}
```

## 🚀 DEPLOYMENT & MONITORING

### Production Environment ✅
```env
# Production Environment Variables
DATABASE_URL=********************************/gudang_prod
DIRECTUS_URL=https://directus.gudang.com
DIRECTUS_TOKEN=***

ACCURATE_CLIENT_ID=***
ACCURATE_CLIENT_SECRET=***
ACCURATE_REDIRECT_URI=https://gudang.com/api/accurate/callback

NEXTAUTH_SECRET=***
NEXTAUTH_URL=https://gudang.com
```

### Monitoring & Logging ✅
```typescript
// Error Tracking
const logError = (error: Error, context: any) => {
  console.error('System Error:', {
    message: error.message,
    stack: error.stack,
    context,
    timestamp: new Date().toISOString()
  })
}

// Performance Monitoring
const trackPerformance = (operation: string, duration: number) => {
  console.log('Performance Metric:', {
    operation,
    duration,
    timestamp: new Date().toISOString()
  })
}
```

## 🔮 FUTURE TECHNICAL ROADMAP

### Phase 6: Mobile App Development
- **React Native**: Cross-platform mobile application
- **Offline Support**: Local storage with sync capabilities
- **Camera Integration**: Barcode scanning functionality
- **Push Notifications**: Real-time stock alerts

### Phase 7: Advanced Analytics
- **Data Warehouse**: Separate analytics database
- **ETL Pipeline**: Data processing and aggregation
- **Machine Learning**: Demand forecasting models
- **Business Intelligence**: Advanced reporting dashboard

### Phase 8: Enterprise Features
- **Multi-tenant Architecture**: Support multiple companies
- **Advanced Security**: Role-based permissions, audit logs
- **API Gateway**: Rate limiting, authentication, monitoring
- **Microservices**: Scalable service architecture

---

*Document Type: Technical Context*  
*Last Updated: 2024-12-27*  
*Version: 2.0 - Production System*  
*Classification: Internal* 