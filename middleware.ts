import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Add paths that should be accessible without authentication
const publicPaths = ['/login', '/api/auth', '/api/accurate/', '/api/sync/']

// Protected API paths that require authentication
const protectedApiPaths = [
  '/api/stock/',
  '/api/products/',
  '/api/vendors/',
  '/api/production/',
  '/api/dashboard/'
]

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Allow public paths
  if (publicPaths.some(path => pathname.startsWith(path))) {
    return NextResponse.next()
  }

  // For client-side routes, let the client handle auth
  if (pathname.startsWith('/dashboard') || pathname.startsWith('/test-auth')) {
    return NextResponse.next()
  }

  // Log access to sensitive routes for monitoring
  if (pathname.includes('/bulk-movement/history') ||
      pathname.includes('/stock/reports') ||
      pathname.includes('/production')) {
    console.log(`📊 Access to sensitive route: ${pathname} at ${new Date().toISOString()}`)
  }

  // For protected API routes, check Authorization header
  if (pathname.startsWith('/api/') && !publicPaths.some(path => pathname.startsWith(path))) {
    const authHeader = request.headers.get('authorization')

    // Check if this is a protected API path
    const isProtectedApi = protectedApiPaths.some(path => pathname.startsWith(path))

    if (isProtectedApi && !authHeader) {
      console.log(`🔒 Unauthorized API access attempt: ${pathname}`)
      return new NextResponse(
        JSON.stringify({
          error: 'Unauthorized',
          message: 'Authentication required for this endpoint',
          path: pathname
        }),
        { status: 401, headers: { 'content-type': 'application/json' } }
      )
    }

    // Validate token format for protected APIs
    if (isProtectedApi && authHeader) {
      const token = authHeader.replace('Bearer ', '')
      if (!token || token.length < 10) {
        console.log(`🔒 Invalid token format for: ${pathname}`)
        return new NextResponse(
          JSON.stringify({
            error: 'Invalid token',
            message: 'Please login again',
            path: pathname
          }),
          { status: 401, headers: { 'content-type': 'application/json' } }
        )
      }
    }

    return NextResponse.next()
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
}