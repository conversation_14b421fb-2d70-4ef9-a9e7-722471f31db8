# Test Plan untuk Perbaikan Bulk Movement

## Masalah yang Diperbaiki

### 1. Double Submit Prevention
- ✅ Menambahkan state `isSubmitting` yang lebih robust
- ✅ Menambahkan timeout protection (30 detik)
- ✅ Menambahkan tracking submit attempts untuk debugging
- ✅ Menambahkan visual feedback dengan loading spinner
- ✅ Disable tombol Cancel saat submitting
- ✅ Cleanup timeout pada success/error

### 2. Keyboard Shortcuts untuk Windows
- ✅ Memperbaiki event handling dengan `capture: true`
- ✅ Menambahkan `stopPropagation()` untuk mencegah konflik
- ✅ Support untuk `e.key.toLowerCase()` untuk konsistensi
- ✅ Menambahkan support `Backspace` sebagai alternatif `Delete`
- ✅ Mencegah shortcuts saat user mengetik di input fields
- ✅ Enhanced debugging logs untuk troubleshooting
- ✅ Menambahkan tips khusus untuk Windows users

## Test Cases

### Test Double Submit Prevention

1. **Test Basic Double Click**
   - Isi form dengan data valid
   - Klik tombol "Create Bulk Movement" 2x dengan cepat
   - ✅ Expected: Hanya 1 request yang dikirim, toast warning muncul

2. **Test Keyboard + Mouse Combo**
   - Isi form dengan data valid
   - Te<PERSON> Ctrl+Enter, lalu langsung klik tombol submit
   - ✅ Expected: Hanya 1 request yang dikirim

3. **Test Timeout Protection**
   - Simulasi network delay (bisa dengan throttling di DevTools)
   - Submit form dan tunggu 30+ detik
   - ✅ Expected: State reset otomatis setelah 30 detik

### Test Keyboard Shortcuts Windows

1. **Test Ctrl+Enter (Submit)**
   - Windows + Chrome: Tekan Ctrl+Enter
   - Windows + Edge: Tekan Ctrl+Enter
   - ✅ Expected: Form submit

2. **Test Alt+A (Add Item)**
   - Windows + Chrome: Tekan Alt+A
   - Windows + Edge: Tekan Alt+A
   - ✅ Expected: Item baru ditambahkan

3. **Test Ctrl+Shift+A (Backup Add Item)**
   - Windows + Chrome: Tekan Ctrl+Shift+A
   - Windows + Edge: Tekan Ctrl+Shift+A
   - ✅ Expected: Item baru ditambahkan

4. **Test Ctrl+Delete/Backspace (Delete Item)**
   - Fokus pada item (klik pada border item)
   - Windows + Chrome: Tekan Ctrl+Delete atau Ctrl+Backspace
   - Windows + Edge: Tekan Ctrl+Delete atau Ctrl+Backspace
   - ✅ Expected: Item terhapus (jika lebih dari 1 item)

5. **Test Input Field Protection**
   - Fokus pada input field (quantity, notes, dll)
   - Tekan Alt+A
   - ✅ Expected: Shortcut tidak berfungsi (kecuali Ctrl+Enter)

## Debugging Tools

### Console Logs
- `🎹 Keyboard shortcut detected:` - Log detail event keyboard
- `📝 Form submission attempt #X` - Track submit attempts
- `⚠️ Form is already submitting` - Double submit prevention
- `🚀 Submitting form via keyboard shortcut` - Keyboard submit
- `➕ Adding new item via X shortcut` - Add item shortcuts
- `🗑️ Delete shortcut detected` - Delete item shortcuts

### Browser DevTools
1. Buka DevTools (F12)
2. Tab Console untuk melihat logs
3. Tab Network untuk monitor requests
4. Tab Application > Local Storage untuk cek token

## Manual Testing Steps

### Setup
1. Login ke aplikasi
2. Navigate ke `/stock/bulk-movement`
3. Buka DevTools Console

### Test Sequence
1. Test keyboard shortcuts satu per satu
2. Test double submit prevention
3. Test di berbagai browser (Chrome, Edge)
4. Test dengan berbagai input states

### Expected Behavior
- Semua shortcuts berfungsi di Windows
- Tidak ada double submit
- Visual feedback yang jelas
- Error handling yang proper
