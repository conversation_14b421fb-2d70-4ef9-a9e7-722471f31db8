# Test Plan untuk Perbaikan Filter Tanggal Bulk Movement History

## Masalah yang Diperbaiki

### 1. Filter Tanggal Tidak Berfungsi
- ✅ **Simplified API approach** - Menghindari complex query yang menyebabkan error 500
- ✅ **Post-grouping filtering** - Filter diterapkan setelah data di-group
- ✅ **Enhanced date parsing** - Proper date comparison dengan timezone handling
- ✅ **Visual feedback** - Menampilkan range tanggal yang aktif + toast notification
- ✅ **Enhanced debugging** - Comprehensive logs untuk troubleshooting
- ✅ **Fallback method** - Robust error handling dengan fallback approach

## Root Cause Analysis

### Masalah Utama yang Ditemukan:
1. **Complex Query Error 500** - Directus API tidak bisa handle filter kompleks dengan `_and` dan `_or`
2. **Post-grouping Filter Missing** - Filter tanggal hilang setelah data di-group by reference_number
3. **Date Format Issues** - Timezone dan inclusive date filtering tidak proper

### Error Log yang Ditemukan:
```
🔍 API Query: https://api.iderkopi.id/items/StockMovement?filter=...
⚠️ Direct API failed, trying fallback method: Error: API error: 500
```

## Perbaikan yang Diterapkan

### Backend (API) - `/api/stock/bulk-movement/history/route.ts`

1. **Simplified API Approach:**
   ```typescript
   // Sebelum: Complex query yang menyebabkan error 500
   finalFilters = {
     _and: [
       { _or: [bulk movement filters] },
       { date: { _between: [dateFrom, dateTo] } }
     ]
   }

   // Sesudah: Simple query + post-processing
   const simpleFilter = {
     _or: [
       { reference_number: { _starts_with: 'BULK-' } },
       { notes: { _contains: 'Bulk Movement:' } }
     ]
   }
   ```

2. **Post-Grouping Filtering:**
   ```typescript
   // Filter diterapkan setelah data di-group
   if (dateFrom || dateTo) {
     bulkMovementsArray = bulkMovementsArray.filter(bulk => {
       const bulkDate = new Date(bulk.date)
       const fromDate = new Date(dateFrom)
       fromDate.setHours(0, 0, 0, 0) // Start of day
       const toDate = new Date(dateTo)
       toDate.setHours(23, 59, 59, 999) // End of day
       // Proper date comparison
     })
   }
   ```

3. **Enhanced Error Handling:**
   - Fallback method yang robust
   - Detailed error logging
   - Graceful degradation
   - Debug information dalam response

### Frontend - `/app/stock/bulk-movement/history/page.tsx`

1. **Improved Parameter Building:**
   ```typescript
   // Sebelum: spread operator bisa skip empty values
   ...(dateFrom && { dateFrom }),
   ...(dateTo && { dateTo })

   // Sesudah: explicit parameter building
   if (dateFrom) params.append('dateFrom', dateFrom)
   if (dateTo) params.append('dateTo', dateTo)
   ```

2. **Visual Feedback:**
   - Menampilkan range tanggal aktif
   - Indicator filter yang sedang aktif
   - Disable clear button jika tidak ada filter

3. **Enhanced Debugging:**
   - Log perubahan tanggal
   - Log parameter yang dikirim
   - Log response dari API

## Test Cases

### Test 1: Filter Tanggal Tunggal

**Test From Date:**
1. Set "Date From" ke tanggal tertentu (misal: 2024-01-01)
2. Biarkan "Date To" kosong
3. Klik refresh atau tunggu auto-reload
4. ✅ Expected: Hanya menampilkan bulk movements dari tanggal tersebut ke depan

**Test To Date:**
1. Set "Date To" ke tanggal tertentu (misal: 2024-12-31)
2. Biarkan "Date From" kosong
3. Klik refresh atau tunggu auto-reload
4. ✅ Expected: Hanya menampilkan bulk movements sampai tanggal tersebut

### Test 2: Filter Range Tanggal

**Test Date Range:**
1. Set "Date From" ke 2024-01-01
2. Set "Date To" ke 2024-01-31
3. Klik refresh atau tunggu auto-reload
4. ✅ Expected: Hanya menampilkan bulk movements dalam range Januari 2024

### Test 3: Kombinasi Filter

**Test Date + Type:**
1. Set date range
2. Set type filter (incoming/outgoing)
3. ✅ Expected: Filter bekerja kombinasi

**Test Date + Vendor:**
1. Set date range
2. Set vendor filter
3. ✅ Expected: Filter bekerja kombinasi

**Test Date + Search:**
1. Set date range
2. Input search term
3. ✅ Expected: Filter bekerja kombinasi

### Test 4: Edge Cases

**Test Same Date:**
1. Set "Date From" dan "Date To" ke tanggal yang sama
2. ✅ Expected: Menampilkan bulk movements di tanggal tersebut

**Test Invalid Date:**
1. Manually input invalid date (jika memungkinkan)
2. ✅ Expected: Graceful handling, tidak crash

**Test Future Date:**
1. Set date range ke masa depan
2. ✅ Expected: Tidak ada hasil, tidak error

## Debugging Tools

### Console Logs untuk Monitoring

**Frontend Logs:**
- `📅 Date From changed:` - Perubahan input tanggal dari
- `📅 Date To changed:` - Perubahan input tanggal ke
- `📅 Frontend date filter debug:` - Debug parameter yang dikirim
- `✅ API Response:` - Response dari API
- `📅 Date filter results:` - Hasil filtering tanggal

**Backend Logs:**
- `📅 Date filter (between):` - Filter range tanggal
- `📅 Date filter (from):` - Filter dari tanggal
- `📅 Date filter (to):` - Filter sampai tanggal
- `📊 Before filtering:` - Jumlah sebelum filter
- `📅 After date filter:` - Jumlah setelah filter tanggal

### Browser DevTools

1. **Console Tab:**
   - Monitor logs untuk debugging
   - Check error messages

2. **Network Tab:**
   - Monitor API calls ke `/api/stock/bulk-movement/history`
   - Check query parameters
   - Check response data

3. **Application Tab:**
   - Check localStorage untuk token

## Manual Testing Steps

### Setup
1. Login ke aplikasi
2. Navigate ke `/stock/bulk-movement/history`
3. Buka DevTools Console

### Test Sequence
1. **Test tanpa filter** - Pastikan data muncul normal
2. **Test filter tanggal satu per satu** - From, To, Range
3. **Test kombinasi filter** - Date + Type, Date + Vendor, dll
4. **Test clear filters** - Pastikan reset dengan benar
5. **Test refresh** - Pastikan filter tetap aktif

### Expected Behavior
- Filter tanggal bekerja dengan benar
- Visual feedback menunjukkan filter aktif
- Kombinasi filter bekerja
- Performance tetap baik
- Error handling yang proper

## Troubleshooting

Jika filter masih tidak bekerja:

1. **Check Console Logs** - Lihat error atau warning
2. **Check Network Tab** - Pastikan parameter terkirim
3. **Check Date Format** - Pastikan format YYYY-MM-DD
4. **Check Data** - Pastikan ada data dalam range tanggal
5. **Check Token** - Pastikan authentication valid
